// External dependencies
import { useEffect, useState } from 'react';
import { Head, Link, useForm, usePage } from '@inertiajs/react';
import type { PageProps as InertiaPageProps } from '@inertiajs/core';
import { ArrowLeft, Save, AlertCircle } from 'lucide-react';

// Layout and components
import AppLayout from '@/layouts/app-layout';
import { FormImageUpload } from '@/components/FormImageUpload';

// UI Components
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Switch } from '@/components/ui/switch';
import { Textarea } from '@/components/ui/textarea';

// Types
interface User {
    id: number;
    name: string;
    email: string;
    email_verified_at: string | null;
    created_at: string;
    updated_at: string;
    [key: string]: unknown;
}

interface PageProps extends InertiaPageProps {
    auth: {
        user: User;
    };
    errors: Record<string, string>;
    vehicle?: Vehicle;
    brands: Array<{ id: number; name: string }>;
    categories: Array<{ id: number; name: string }>;
    fuelTypes: Record<string, string>;
    features: Array<{ id: number; name: string }>;
    [key: string]: unknown;
}

// Component types
interface MediaFile {
    id: number;
    file_name: string;
    original_url: string;
    size: number;
    mime_type: string;
    collection_name: string;
}

interface Vehicle {
    id: number;
    brand_id: number;
    category_id: number;
    model: string;
    year_manufacture: number;
    model_year: number;
    license_plate: string;
    chassis_number: string;
    color: string;
    fuel_type: string;
    transmission: string;
    mileage: number;
    description: string;
    price: number;
    promotional_price: number | null;
    is_negotiable: boolean;
    is_featured: boolean;
    status: string;
    seo_title: string;
    seo_description: string;
    seo_keywords: string;
    features: Array<{ id: number }>;
    media: MediaFile[];
}

interface PageProps extends BasePageProps {
    errors: Record<string, string>;
    vehicle?: Vehicle;
    brands: Array<{ id: number; name: string }>;
    categories: Array<{ id: number; name: string }>;
    fuelTypes: Record<string, string>;
    features: Array<{ id: number; name: string }>;
}

interface ImageFile {
    id: string;
    file: File;
    preview: string;
    name: string;
    size: number;
    type: string;
}

export default function VehicleForm() {
    const { 
        errors, 
        vehicle, 
        brands = [], 
        categories = [], 
        fuelTypes = {}
    } = usePage<PageProps>().props;
    
    const isEdit = !!vehicle;
    
    // Define form data type
    type VehicleFormData = {
    images: File[];
    featured_image: File | null;
    brand_id: string;
    category_id: string;
    model: string;
    year_manufacture: string;
    model_year: string;
    license_plate: string;
    chassis_number: string;
    color: string;
    fuel_type: string;
    transmission: string;
    mileage: string;
    description: string;
    price: string;
    promotional_price: string | null;
    is_negotiable: boolean;
    is_featured: boolean;
    status: string;
    seo_title: string;
    seo_description: string;
    seo_keywords: string;
    features: string[];
    deleted_images?: string[];
}

type InertiaPostOptions = {
    forceFormData: boolean;
    onError?: (errors: Record<string, string>) => void;
    onFinish?: () => void;
    [key: string]: unknown;
};

    const { data, setData, post, processing } = useForm<VehicleFormData>({
        images: [],
        featured_image: null,
        brand_id: vehicle?.brand_id?.toString() || '',
        category_id: vehicle?.category_id?.toString() || '',
        model: vehicle?.model || '',
        year_manufacture: vehicle?.year_manufacture?.toString() || '',
        model_year: vehicle?.model_year?.toString() || '',
        license_plate: vehicle?.license_plate || '',
        chassis_number: vehicle?.chassis_number || '',
        color: vehicle?.color || '',
        fuel_type: vehicle?.fuel_type || 'gasoline',
        transmission: vehicle?.transmission || 'manual',
        mileage: vehicle?.mileage?.toString() || '0',
        description: vehicle?.description || '',
        price: vehicle?.price ? (vehicle.price / 100).toFixed(2) : '',
        promotional_price: vehicle?.promotional_price ? (vehicle.promotional_price / 100).toFixed(2) : '',
        is_negotiable: vehicle?.is_negotiable || false,
        is_featured: vehicle?.is_featured || false,
        status: vehicle?.status || 'draft',
        seo_title: vehicle?.seo_title || '',
        seo_description: vehicle?.seo_description || '',
        seo_keywords: vehicle?.seo_keywords || '',
        features: vehicle?.features?.map(f => f.id.toString()) || [],
    });

    // Form state
    const [selectedBrand, setSelectedBrand] = useState<string>(vehicle?.brand_id?.toString() || '');
    const [selectedCategory, setSelectedCategory] = useState<string>(vehicle?.category_id?.toString() || '');
    const [selectedFuelType, setSelectedFuelType] = useState<string>(vehicle?.fuel_type || 'gasoline');
    const [selectedStatus, setSelectedStatus] = useState<string>(vehicle?.status || 'draft');
    const [images, setImages] = useState<ImageFile[]>([]);
    const [featuredImage, setFeaturedImage] = useState<ImageFile | null>(null);
    const [deletedImageIds] = useState<string[]>([]);
    const [isSubmitting, setIsSubmitting] = useState(false);

    // Carregar imagens existentes ao editar
    useEffect(() => {
        if (isEdit && vehicle?.media) {
            // Carregar imagens comuns
            const mediaFiles = vehicle.media
                .filter((media: { collection_name: string }) => media.collection_name === 'images')
                .map((media: { id: number; file_name: string; original_url: string; size: number; mime_type: string }) => {
                    const file = new File([], media.file_name, { type: media.mime_type });
                    return {
                        id: media.id.toString(),
                        file,
                        preview: media.original_url,
                        name: media.file_name,
                        size: media.size,
                        type: media.mime_type,
                    } as ImageFile;
                });
            
            setImages(mediaFiles);

            // Carregar imagem destacada
            const featuredMedia = vehicle.media.find((media: { collection_name: string }) => media.collection_name === 'featured_image');
            if (featuredMedia) {
                const file = new File([], featuredMedia.file_name, { type: featuredMedia.mime_type });
                setFeaturedImage({
                    id: featuredMedia.id.toString(),
                    file,
                    preview: featuredMedia.original_url,
                    name: featuredMedia.file_name,
                    size: featuredMedia.size,
                    type: featuredMedia.mime_type,
                } as ImageFile);
            }
        }
    }, [isEdit, vehicle?.media]);

    // Update form data when images or deleted images change
    useEffect(() => {
        // Convert ImageFile[] to File[] for form data
        const files = images.map(img => img.file);
        setData('images', files as unknown as File[]);
        setData('deleted_images', deletedImageIds);
    }, [images, deletedImageIds, setData]);

    // Update form data when selections change
    useEffect(() => {
        if (selectedBrand) setData('brand_id', selectedBrand);
        if (selectedCategory) setData('category_id', selectedCategory);
        if (selectedFuelType) setData('fuel_type', selectedFuelType);
        if (selectedStatus) setData('status', selectedStatus);
        
    }, [
        selectedBrand, 
        selectedCategory, 
        selectedFuelType, 
        selectedStatus, 
        setData
    ]);

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();
        
        try {
            setIsSubmitting(true);
            const formData = new FormData();
            
            // Add all form data
            (Object.entries(data) as [keyof VehicleFormData, unknown][]).forEach(([key, value]) => {
                if (value === null || value === undefined || value === '') return;
                
                if (key === 'images' && Array.isArray(value)) {
                    // Add images
                    images.forEach((file) => {
                        formData.append('images[]', file.file);
                    });
                } else if (key === 'deleted_images' && Array.isArray(value)) {
                    // Add deleted image IDs
                    value.forEach((id) => {
                        formData.append('deleted_images[]', id);
                    });
                } else if (key === 'featured_image' && featuredImage) {
                    // Add featured image
                    formData.append('featured_image', featuredImage.file);
                } else if (typeof value === 'boolean') {
                    formData.append(key, value ? '1' : '0');
                } else if (Array.isArray(value)) {
                    // Handle arrays (like features)
                    value.forEach((item) => {
                        formData.append(`${key}[]`, item);
                    });
                } else {
                    // Add other form fields
                    formData.append(key, value.toString());
                }
            });
            
            // Add deleted images
            deletedImageIds.forEach((id) => {
                formData.append('deleted_images[]', id);
            });
            
            const url = isEdit && vehicle 
                ? route('admin.vehicles.update', { id: vehicle.id })
                : route('admin.vehicles.store');
            
            // For PUT requests, we need to add _method
            if (isEdit && vehicle) {
                formData.append('_method', 'PUT');
            }
            
            // Submit the form with proper typing
            const options: InertiaPostOptions = {
                forceFormData: true,
                onError: (errors: Record<string, string>) => {
                    console.error('Form submission error:', errors);
                    // You can add toast notification here if needed
                },
                onFinish: () => {
                    setIsSubmitting(false);
                }
            };
            
            // Use type assertion to handle Inertia's post method
            type InertiaPost = (url: string, data: FormData, options: InertiaPostOptions) => Promise<void>;
            
            const postData = async () => {
                if (isEdit && vehicle) {
                    return await (post as unknown as InertiaPost)(url, formData, options);
                } else {
                    return await (post as unknown as InertiaPost)(url, formData, options);
                }
            };
            
            await postData();
        } catch (error) {
            console.error('Error submitting form:', error);
            // Handle unexpected errors
            setIsSubmitting(false);
            // You can add toast notification here if needed
        }
    };


    const handlePriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.replace(/\D/g, '');
        const number = parseFloat(value) / 100;
        setData('price', isNaN(number) ? '' : number.toString());
    };

    const formatCurrency = (value: string): string => {
        if (!value) return '';
        const number = parseFloat(value);
        if (isNaN(number)) return '';
        return number.toLocaleString('pt-BR', {
            minimumFractionDigits: 2,
            maximumFractionDigits: 2
        });
    };

    const handlePromotionalPriceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
        const value = e.target.value.replace(/\D/g, '');
        const number = parseFloat(value) / 100;
        setData('promotional_price', isNaN(number) ? '' : number.toString());
    };

    return (
        <AppLayout>
            <Head title={isEdit ? 'Editar Veículo' : 'Adicionar Veículo'} />
            
            <div className="space-y-6">
                {Object.keys(errors).length > 0 && (
                    <div className="bg-red-50 border-l-4 border-red-400 p-4 mb-6">
                        <div className="flex">
                            <div className="flex-shrink-0">
                                <AlertCircle className="h-5 w-5 text-red-400" aria-hidden="true" />
                            </div>
                            <div className="ml-3">
                                <p className="text-sm text-red-700">
                                    Ocorreram erros ao processar o formulário. Por favor, verifique os campos destacados.
                                </p>
                            </div>
                        </div>
                    </div>
                )}
                <div className="flex items-center justify-between">
                    <div>
                        <h2 className="text-2xl font-bold tracking-tight">
                            {isEdit ? 'Editar Veículo' : 'Adicionar Veículo'}
                        </h2>
                        <p className="text-muted-foreground">
                            {isEdit 
                                ? 'Atualize as informações do veículo' 
                                : 'Preencha os dados para cadastrar um novo veículo'}
                        </p>
                    </div>
                    <Button variant="outline" asChild>
                        <Link href={route('admin.vehicles.index')}>
                            <ArrowLeft className="w-4 h-4 mr-2" />
                            Voltar
                        </Link>
                    </Button>
                </div>

                <form onSubmit={handleSubmit} className="space-y-6">
                    <div className="grid gap-6 md:grid-cols-3">
                        {/* Informações Básicas */}
                        <Card className="md:col-span-2">
                            <CardHeader>
                                <CardTitle>Informações do Veículo</CardTitle>
                                <CardDescription>
                                    Preencha as informações básicas do veículo
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="brand_id">Marca *</Label>
                                        <Select 
                                            value={selectedBrand} 
                                            onValueChange={setSelectedBrand}
                                            required
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Selecione a marca" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {brands.map((brand: { id: number; name: string }) => (
                                                    <SelectItem key={brand.id} value={brand.id.toString()}>
                                                        {brand.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.brand_id && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.brand_id}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="model">Modelo *</Label>
                                        <Input
                                            id="model"
                                            value={data.model}
                                            onChange={(e) => setData('model', e.target.value)}
                                            placeholder="Ex: Onix 1.0 Turbo"
                                            required
                                        />
                                        {errors.model && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.model}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="year_manufacture">Ano de Fabricação *</Label>
                                        <Input
                                            id="year_manufacture"
                                            type="number"
                                            min="1900"
                                            max={new Date().getFullYear() + 1}
                                            value={data.year_manufacture}
                                            onChange={(e) => setData('year_manufacture', e.target.value)}
                                            required
                                        />
                                        {errors.year_manufacture && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.year_manufacture}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="model_year">Ano do Modelo *</Label>
                                        <Input
                                            id="model_year"
                                            type="number"
                                            min="1900"
                                            max={new Date().getFullYear() + 1}
                                            value={data.model_year}
                                            onChange={(e) => setData('model_year', e.target.value)}
                                            required
                                        />
                                        {errors.model_year && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.model_year}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="license_plate">Placa</Label>
                                        <Input
                                            id="license_plate"
                                            value={data.license_plate}
                                            onChange={(e) => setData('license_plate', e.target.value.toUpperCase())}
                                            placeholder="AAA-0000"
                                        />
                                        {errors.license_plate && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.license_plate}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="chassis_number">Chassi</Label>
                                        <Input
                                            id="chassis_number"
                                            value={data.chassis_number}
                                            onChange={(e) => setData('chassis_number', e.target.value.toUpperCase())}
                                            placeholder="Número do chassi"
                                        />
                                        {errors.chassis_number && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.chassis_number}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="color">Cor</Label>
                                        <Input
                                            id="color"
                                            value={data.color}
                                            onChange={(e) => setData('color', e.target.value)}
                                            placeholder="Ex: Prata"
                                        />
                                        {errors.color && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.color}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="fuel_type">Combustível</Label>
                                        <Select 
                                            value={selectedFuelType} 
                                            onValueChange={setSelectedFuelType}
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Selecione o combustível" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {Object.entries(fuelTypes as Record<string, string>).map(([value, label]) => (
                                                    <SelectItem key={value} value={value}>
                                                        {label}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.fuel_type && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.fuel_type}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="mileage">Quilometragem</Label>
                                        <div className="relative">
                                            <Input
                                                id="mileage"
                                                type="number"
                                                min="0"
                                                value={data.mileage}
                                                onChange={(e) => setData('mileage', e.target.value)}
                                                placeholder="0"
                                            />
                                            <span className="absolute right-3 top-2.5 text-sm text-muted-foreground">km</span>
                                        </div>
                                        {errors.mileage && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.mileage}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="price">Preço *</Label>
                                        <div className="relative">
                                            <span className="absolute left-3 top-2.5 text-sm text-muted-foreground">R$</span>
                                            <Input
                                                id="price"
                                                className="pl-8"
                                                value={formatCurrency(data.price)}
                                                onChange={handlePriceChange}
                                                placeholder="0,00"
                                                required
                                            />
                                        </div>
                                        {errors.price && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.price}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="promotional_price">Preço Promocional</Label>
                                        <div className="relative">
                                            <span className="absolute left-3 top-2.5 text-sm text-muted-foreground">R$</span>
                                            <Input
                                                id="promotional_price"
                                                className="pl-8"
                                                value={data.promotional_price ? formatCurrency(data.promotional_price) : ''}
                                                onChange={handlePromotionalPriceChange}
                                                placeholder="0,00"
                                            />
                                        </div>
                                        {errors.promotional_price && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.promotional_price}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="category_id">Categoria *</Label>
                                        <Select 
                                            value={selectedCategory} 
                                            onValueChange={setSelectedCategory}
                                            required
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Selecione a categoria" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                {categories.map((category: { id: number; name: string }) => (
                                                    <SelectItem key={category.id} value={category.id.toString()}>
                                                        {category.name}
                                                    </SelectItem>
                                                ))}
                                            </SelectContent>
                                        </Select>
                                        {errors.category_id && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.category_id}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="status">Status *</Label>
                                        <Select 
                                            value={selectedStatus} 
                                            onValueChange={setSelectedStatus}
                                            required
                                        >
                                            <SelectTrigger>
                                                <SelectValue placeholder="Selecione o status" />
                                            </SelectTrigger>
                                            <SelectContent>
                                                <SelectItem value="draft">Rascunho</SelectItem>
                                                <SelectItem value="published">Publicado</SelectItem>
                                                <SelectItem value="sold">Vendido</SelectItem>
                                                <SelectItem value="reserved">Reservado</SelectItem>
                                            </SelectContent>
                                        </Select>
                                        {errors.status && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.status}
                                            </p>
                                        )}
                                    </div>
                                </div>

                                <div className="space-y-2">
                                    <Label htmlFor="description">Descrição</Label>
                                    <Textarea
                                        id="description"
                                        value={data.description}
                                        onChange={(e) => setData('description', e.target.value)}
                                        placeholder="Descreva detalhes importantes sobre o veículo..."
                                        rows={5}
                                    />
                                    {errors.description && (
                                        <p className="text-sm text-red-500 flex items-center gap-1">
                                            <AlertCircle className="w-4 h-4" />
                                            {errors.description}
                                        </p>
                                    )}
                                </div>
                            </CardContent>
                        </Card>

                        {/* Imagens */}
                        <Card className="md:col-span-3">
                            <CardHeader>
                                <CardTitle>Imagens do Veículo</CardTitle>
                                <CardDescription>
                                    Adicione até 10 imagens do veículo. A primeira imagem será a principal.
                                </CardDescription>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <div>
                                    <Label>Imagens do Veículo</Label>
                                    <p className="text-sm text-muted-foreground mb-2">
                                        Adicione até 10 imagens do veículo. A primeira imagem será a principal.
                                    </p>
                                    <FormImageUpload
                                        value={images}
                                        onChange={setImages}
                                        maxFiles={10}
                                        disabled={processing}
                                    />
                                </div>

                                <div>
                                    <Label>Imagem Destacada</Label>
                                    <p className="text-sm text-muted-foreground mb-2">
                                        Selecione uma imagem para ser destacada na listagem.
                                    </p>
                                    <FormImageUpload
                                        value={featuredImage ? [featuredImage] : []}
                                        onChange={(files) => setFeaturedImage(files[0] || null)}
                                        maxFiles={1}
                                        disabled={processing}
                                    />
                                </div>
                            </CardContent>
                        </Card>

                    </div>

                    {/* Configurações Adicionais */}
                    <div className="grid gap-6 md:grid-cols-3">
                        <div className="space-y-6 md:col-span-1">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Configurações</CardTitle>
                                    <CardDescription>
                                        Defina as configurações adicionais do veículo
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="flex items-center justify-between">
                                        <div className="space-y-0.5">
                                            <Label htmlFor="is_negotiable">Aceita Troca?</Label>
                                            <p className="text-sm text-muted-foreground">
                                                Marque se o veículo aceita troca
                                            </p>
                                        </div>
                                        <Switch
                                            id="is_negotiable"
                                            checked={data.is_negotiable}
                                            onCheckedChange={(checked) => setData('is_negotiable', checked)}
                                        />
                                    </div>

                                    <div className="flex items-center justify-between">
                                        <div className="space-y-0.5">
                                            <Label htmlFor="is_featured">Destaque</Label>
                                            <p className="text-sm text-muted-foreground">
                                                Exibir como destaque na página inicial
                                            </p>
                                        </div>
                                        <Switch
                                            id="is_featured"
                                            checked={data.is_featured}
                                            onCheckedChange={(checked) => setData('is_featured', checked)}
                                        />
                                    </div>
                                </CardContent>
                            </Card>

                            {/* SEO */}
                            <Card>
                                <CardHeader>
                                    <CardTitle>Otimização para Mecanismos de Busca (SEO)</CardTitle>
                                    <CardDescription>
                                        Configure as informações de SEO para melhorar a visibilidade do veículo
                                    </CardDescription>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    <div className="space-y-2">
                                        <Label htmlFor="seo_title">Título para SEO</Label>
                                        <Input
                                            id="seo_title"
                                            value={data.seo_title}
                                            onChange={(e) => setData('seo_title', e.target.value)}
                                            placeholder="Ex: Carro Seminovo em Excelente Estado - [Marca] [Modelo]"
                                        />
                                        <p className="text-xs text-muted-foreground">
                                            Título que aparecerá nos resultados de busca (máx. 60 caracteres)
                                        </p>
                                        {errors.seo_title && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.seo_title}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="seo_description">Descrição para SEO</Label>
                                        <Textarea
                                            id="seo_description"
                                            value={data.seo_description}
                                            onChange={(e) => setData('seo_description', e.target.value)}
                                            placeholder="Descrição que aparecerá nos resultados de busca..."
                                            rows={3}
                                        />
                                        <p className="text-xs text-muted-foreground">
                                            Descrição que aparecerá nos resultados de busca (máx. 160 caracteres)
                                        </p>
                                        {errors.seo_description && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.seo_description}
                                            </p>
                                        )}
                                    </div>

                                    <div className="space-y-2">
                                        <Label htmlFor="seo_keywords">Palavras-chave</Label>
                                        <Input
                                            id="seo_keywords"
                                            value={data.seo_keywords}
                                            onChange={(e) => setData('seo_keywords', e.target.value)}
                                            placeholder="Ex: carro, seminovo, [marca], [modelo], [ano]"
                                        />
                                        <p className="text-xs text-muted-foreground">
                                            Separe as palavras-chave por vírgula
                                        </p>
                                        {errors.seo_keywords && (
                                            <p className="text-sm text-red-500 flex items-center gap-1">
                                                <AlertCircle className="w-4 h-4" />
                                                {errors.seo_keywords}
                                            </p>
                                        )}
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </div>

                    <div className="grid gap-6 md:grid-cols-3">
                        <div className="space-y-6 md:col-span-3">

                    <div className="flex items-center justify-between">
                        <Button variant="outline" type="button" asChild>
                            <Link href={route('admin.vehicles.index')}>
                                Cancelar
                            </Link>
                        </Button>
                        <div className="flex gap-2">
                            <Button 
                                variant="outline" 
                                type="submit" 
                                name="draft"
                                value="1"
                                disabled={processing || isSubmitting}
                            >
                                {isSubmitting ? 'Salvando...' : 'Salvar como Rascunho'}
                            </Button>
                            <Button 
                                type="submit" 
                                name="publish"
                                value="1"
                                disabled={processing || isSubmitting}
                            >
                                <Save className="w-4 h-4 mr-2" />
                                {isSubmitting 
                                    ? 'Processando...' 
                                    : isEdit 
                                        ? 'Atualizar Veículo' 
                                        : 'Cadastrar Veículo'}
                            </Button>
                        </div>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </AppLayout>
    );
}
