import { Head, <PERSON>, router } from '@inertiajs/react';
import AdminLayout from '@/components/Layout/AdminLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader,  } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Badge } from '@/components/ui/badge';
import { formatCurrency } from '@/lib/utils';
import { Search, Plus, Edit, Trash2, Eye } from 'lucide-react';
import { useState } from 'react';

interface Image {
  id: number;
  url: string;
  is_featured: boolean;
  created_at: string;
  updated_at: string;
}

interface Brand {
  id: number;
  name: string;
}

interface Vehicle {
  id: number;
  brand: Brand;
  model: string;
  year_manufacture: number;
  price: number;
  promotional_price?: number;
  is_negotiable: boolean;
  status: 'available' | 'sold' | 'reserved' | 'draft' | 'published';
  created_at: string;
  updated_at: string;
  user: {
    name: string;
  };
  license_plate?: string;
  views?: number;
  slug?: string;
  featured_image_url?: string;
  images?: Image[];
}

interface Filters {
  search?: string;
  status?: string;
}

interface PaginationLink {
  url: string | null;
  label: string;
  active: boolean;
}

interface VehicleIndexProps {
  vehicles: {
    data: Vehicle[];
    links: PaginationLink[];
    current_page: number;
    last_page: number;
    per_page: number;
    total: number;
    from: number;
    to: number;
    prev_page_url: string | null;
    next_page_url: string | null;
    path: string;
  };
  filters: Filters;
}

export default function VehicleIndex({ vehicles, filters }: VehicleIndexProps) {
    // Remove unused url from usePage()
    const [search, setSearch] = useState<string>(filters.search || '');
    const [status, setStatus] = useState<string>(filters.status || '');
    
    // Type for the status options
    type StatusType = 'available' | 'sold' | 'reserved' | '';

    const handleFilter = (key: string, value: string) => {
        const params = new URLSearchParams(window.location.search);
        
        if (value) {
            params.set(key, value);
        } else {
            params.delete(key);
        }

        router.get(window.location.pathname + '?' + params.toString(), {}, {
            preserveState: true,
            preserveScroll: true,
        });
    };

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        handleFilter('search', search);
    };

    const handleStatusChange = (value: StatusType) => {
        setStatus(value);
        handleFilter('status', value);
    };

    return (
        <AdminLayout>
            <Head title="Gerenciar Veículos" />
            
            <div className="space-y-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h2 className="text-2xl font-bold tracking-tight">Gerenciar Veículos</h2>
                        <p className="text-muted-foreground">
                            Visualize e gerencie todos os veículos cadastrados no sistema
                        </p>
                    </div>
                    <Button asChild>
                        <Link href={route('admin.vehicles.create')}>
                            <Plus className="w-4 h-4 mr-2" />
                            Adicionar Veículo
                        </Link>
                    </Button>
                </div>

                <Card>
                    <CardHeader>
                        <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
                            <div className="w-full md:w-1/2">
                                <form onSubmit={handleSearch} className="flex gap-2">
                                    <Input
                                        placeholder="Buscar veículos..."
                                        value={search}
                                        onChange={(e) => setSearch(e.target.value)}
                                        className="max-w-sm"
                                    />
                                    <Button type="submit" variant="outline">
                                        <Search className="w-4 h-4 mr-2" />
                                        Buscar
                                    </Button>
                                </form>
                            </div>
                            <div className="flex gap-2">
                                <Select 
                                    value={status} 
                                    onValueChange={(value: string) => handleStatusChange(value as StatusType)}
                                >
                                    <SelectTrigger className="w-[180px]">
                                        <SelectValue placeholder="Status" />
                                    </SelectTrigger>
                                    <SelectContent>
                                        <SelectItem value="">Todos os status</SelectItem>
                                        <SelectItem value="published">Publicado</SelectItem>
                                        <SelectItem value="draft">Rascunho</SelectItem>
                                        <SelectItem value="sold">Vendido</SelectItem>
                                        <SelectItem value="reserved">Reservado</SelectItem>
                                    </SelectContent>
                                </Select>
                            </div>
                        </div>
                    </CardHeader>
                    <CardContent>
                        <div className="rounded-md border">
                            <Table>
                                <TableHeader>
                                    <TableRow>
                                        <TableHead>Veículo</TableHead>
                                        <TableHead>Ano</TableHead>
                                        <TableHead>Preço</TableHead>
                                        <TableHead>Status</TableHead>
                                        <TableHead>Visualizações</TableHead>
                                        <TableHead className="text-right">Ações</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {vehicles.data.length === 0 ? (
                                        <TableRow>
                                            <TableCell colSpan={6} className="text-center py-8 text-muted-foreground">
                                                Nenhum veículo encontrado
                                            </TableCell>
                                        </TableRow>
                                    ) : (
                                        vehicles.data.map((vehicle: Vehicle) => (
                                            <TableRow key={vehicle.id}>
                                                <TableCell className="font-medium">
                                                    <div className="flex items-center gap-3">
                                                        {vehicle.images?.[0]?.url ? (
                                                            <img 
                                                                src={vehicle.images[0].url} 
                                                                alt={vehicle.model}
                                                                className="h-12 w-16 rounded-md object-cover"
                                                            />
                                                        ) : (
                                                            <div className="h-12 w-16 rounded-md bg-muted flex items-center justify-center">
                                                                <span className="text-xs text-muted-foreground">Sem imagem</span>
                                                            </div>
                                                        )}
                                                        <div>
                                                            <div className="font-medium">{vehicle.brand.name} {vehicle.model}</div>
                                                            <div className="text-sm text-muted-foreground">{vehicle.license_plate || 'Sem placa'}</div>
                                                        </div>
                                                    </div>
                                                </TableCell>
                                                <TableCell>{vehicle.year_manufacture}</TableCell>
                                                <TableCell>
                                                    {vehicle.promotional_price ? (
                                                        <div>
                                                            <span className="line-through text-muted-foreground text-sm">
                                                                {formatCurrency(vehicle.price)}
                                                            </span>
                                                            <div className="font-semibold text-red-600">
                                                                {formatCurrency(vehicle.promotional_price)}
                                                            </div>
                                                        </div>
                                                    ) : (
                                                        formatCurrency(vehicle.price)
                                                    )}
                                                </TableCell>
                                                <TableCell>
                                                    <Badge 
                                                        variant={
                                                            vehicle.status === 'published' ? 'default' :
                                                            vehicle.status === 'draft' ? 'secondary' :
                                                            vehicle.status === 'sold' ? 'destructive' :
                                                            'outline'
                                                        }
                                                    >
                                                        {vehicle.status === 'published' ? 'Publicado' :
                                                         vehicle.status === 'draft' ? 'Rascunho' :
                                                         vehicle.status === 'sold' ? 'Vendido' :
                                                         vehicle.status === 'reserved' ? 'Reservado' : vehicle.status}
                                                    </Badge>
                                                </TableCell>
                                                <TableCell>{vehicle.views}</TableCell>
                                                <TableCell>
                                                    <div className="flex justify-end gap-2">
                                                        <Button variant="ghost" size="icon" asChild>
                                                            {vehicle.slug && (
                                                                <Link href={route('vehicles.show', { slug: vehicle.slug })} target="_blank">
                                                                    <Eye className="h-4 w-4" />
                                                                    <span className="sr-only">Visualizar</span>
                                                                </Link>
                                                            )}
                                                        </Button>
                                                        <Button variant="ghost" size="icon" asChild>
                                                            <Link href={route('admin.vehicles.edit', { vehicle: vehicle.id })}>
                                                                <Edit className="h-4 w-4" />
                                                                <span className="sr-only">Editar</span>
                                                            </Link>
                                                        </Button>
                                                        <Button variant="ghost" size="icon" className="text-destructive hover:text-destructive">
                                                            <Trash2 className="h-4 w-4" />
                                                            <span className="sr-only">Excluir</span>
                                                        </Button>
                                                    </div>
                                                </TableCell>
                                            </TableRow>
                                        ))
                                    )}
                                </TableBody>
                            </Table>
                        </div>

                        {/* Paginação */}
                        {vehicles.last_page > 1 && (
                            <div className="mt-4 flex items-center justify-between">
                                <div className="text-sm text-muted-foreground">
                                    Mostrando <span className="font-medium">{vehicles.from}</span> a{' '}
                                    <span className="font-medium">{vehicles.to}</span> de{' '}
                                    <span className="font-medium">{vehicles.total}</span> veículos
                                </div>
                                <div className="flex gap-2">
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => {
                                            if (vehicles.prev_page_url) {
                                                router.get(vehicles.prev_page_url, {}, {
                                                    preserveState: true,
                                                    preserveScroll: true,
                                                });
                                            }
                                        }}
                                        disabled={!vehicles.prev_page_url}
                                    >
                                        Anterior
                                    </Button>
                                    <Button
                                        variant="outline"
                                        size="sm"
                                        onClick={() => {
                                            if (vehicles.next_page_url) {
                                                router.get(vehicles.next_page_url, {}, {
                                                    preserveState: true,
                                                    preserveScroll: true,
                                                });
                                            }
                                        }}
                                        disabled={!vehicles.next_page_url}
                                    >
                                        Próximo
                                    </Button>
                                </div>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AdminLayout>
    );
}
