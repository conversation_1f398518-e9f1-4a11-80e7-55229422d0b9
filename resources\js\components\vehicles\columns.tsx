import { ColumnDef } from "@tanstack/react-table"
import { format } from "date-fns"
import { ptBR } from "date-fns/locale"
import { ArrowUpDown, MoreHorizontal } from "lucide-react"

import { But<PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Badge } from "@/components/ui/badge"
import { DataTableColumnHeader } from "@/components/ui/data-table-column-header"

export const columns: ColumnDef<any>[] = [
  {
    id: "select",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label="Selecionar todos"
        className="translate-y-[2px]"
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label="Selecionar linha"
        className="translate-y-[2px]"
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "model",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Modelo" />
    ),
    cell: ({ row }) => {
      const vehicle = row.original
      return (
        <div className="flex space-x-2">
          <span className="max-w-[200px] truncate font-medium">
            {vehicle.brand.name} {vehicle.model}
          </span>
        </div>
      )
    },
  },
  {
    accessorKey: "year_manufacture",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Ano" />
    ),
    cell: ({ row }) => {
      const vehicle = row.original
      return (
        <div className="flex space-x-2">
          <span className="max-w-[100px] truncate">
            {vehicle.year_manufacture}/{vehicle.model_year}
          </span>
        </div>
      )
    },
  },
  {
    accessorKey: "mileage",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Quilometragem" />
    ),
    cell: ({ row }) => {
      const vehicle = row.original
      return (
        <div className="flex space-x-2">
          <span className="max-w-[100px] truncate">
            {new Intl.NumberFormat('pt-BR').format(vehicle.mileage)} km
          </span>
        </div>
      )
    },
  },
  {
    accessorKey: "price",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Preço" />
    ),
    cell: ({ row }) => {
      const amount = parseFloat(row.getValue("price"))
      const formatted = new Intl.NumberFormat("pt-BR", {
        style: "currency",
        currency: "BRL",
      }).format(amount)
 
      return <div className="font-medium">{formatted}</div>
    },
  },
  {
    accessorKey: "status",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Status" />
    ),
    cell: ({ row }) => {
      const status = row.getValue("status")
      const variant = {
        published: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200",
        draft: "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",
        pending: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200",
        sold: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200",
        reserved: "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200",
        inactive: "bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-300",
      }[status as string] || "bg-gray-100 text-gray-800"

      return (
        <div className="flex items-center">
          <Badge className={variant}>
            {status === 'published' && 'Publicado'}
            {status === 'draft' && 'Rascunho'}
            {status === 'pending' && 'Pendente'}
            {status === 'sold' && 'Vendido'}
            {status === 'reserved' && 'Reservado'}
            {status === 'inactive' && 'Inativo'}
          </Badge>
        </div>
      )
    },
    filterFn: (row, id, value) => {
      return value.includes(row.getValue(id))
    },
  },
  {
    accessorKey: "created_at",
    header: ({ column }) => (
      <DataTableColumnHeader column={column} title="Criado em" />
    ),
    cell: ({ row }) => {
      const date = new Date(row.getValue("created_at"))
      return (
        <div className="text-sm text-muted-foreground">
          {format(date, "dd/MM/yyyy", { locale: ptBR })}
        </div>
      )
    },
  },
  {
    id: "actions",
    cell: ({ row }) => {
      const vehicle = row.original

      return (
        <div className="flex justify-end">
          <Button
            variant="ghost"
            size="icon"
            onClick={() => window.open(`/veiculos/${vehicle.slug}`, '_blank')}
            className="h-8 w-8 p-0"
          >
            <span className="sr-only">Visualizar</span>
            <Eye className="h-4 w-4" />
          </Button>
          <Button
            variant="ghost"
            size="icon"
            onClick={() => window.location.href = `/admin/veiculos/${vehicle.id}/editar`}
            className="h-8 w-8 p-0"
          >
            <span className="sr-only">Editar</span>
            <Pencil className="h-4 w-4" />
          </Button>
        </div>
      )
    },
  },
]
