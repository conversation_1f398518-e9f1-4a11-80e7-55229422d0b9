import { Link } from '@inertiajs/react';
import { Card, CardContent } from '@/components/ui/card';

interface Category {
  id: number;
  name: string;
  slug: string;
  description: string;
  icon?: string;
  image?: string;
  url: string;
  vehicles_count?: number;
  parts_count?: number;
}

interface CategoryGridProps {
  categories: Category[];
  showCounts?: boolean;
}

export default function CategoryGrid({ categories, showCounts = false }: CategoryGridProps) {
  const getCategoryIcon = (icon: string | undefined) => {
    const iconMap: Record<string, string> = {
      'car': '🚗',
      'bike': '🏍️',
      'wrench': '🔧',
      'truck': '🚚',
      'zap': '⚡',
      'shield': '🛡️',
    };
    
    return iconMap[icon || ''] || '📁';
  };

  const getCategoryImage = (category: Category) => {
    if (category.image) {
      return `/storage/${category.image}`;
    }
    
    // Imagens placeholder baseadas na categoria
    const imageMap: Record<string, string> = {
      'carros': '/placeholder.jpg',
      'motos': '/placeholder.jpg',
      'pecas': '/placeholder.jpg',
      'caminhoes': '/placeholder.jpg',
      'eletricos': '/placeholder.jpg',
      'seguros': '/placeholder.jpg',
    };
    
    return imageMap[category.slug] || '/placeholder.jpg';
  };

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {categories.map((category) => (
        <Link key={category.id} href={category.url}>
          <Card className="group hover:shadow-lg transition-all duration-300 cursor-pointer overflow-hidden">
            <div className="aspect-video bg-gray-100 relative overflow-hidden">
              <img
                src={getCategoryImage(category)}
                alt={category.name}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
              />
              <div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-20 transition-all duration-300" />
              <div className="absolute top-4 left-4">
                <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center shadow-lg">
                  <span className="text-2xl">
                    {getCategoryIcon(category.icon)}
                  </span>
                </div>
              </div>
            </div>
            <CardContent className="p-4">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                {category.name}
              </h3>
              <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                {category.description}
              </p>
              
              {showCounts && (
                <div className="flex justify-between text-sm text-gray-500">
                  {category.vehicles_count !== undefined && (
                    <span>
                      {category.vehicles_count} veículos
                    </span>
                  )}
                  {category.parts_count !== undefined && (
                    <span>
                      {category.parts_count} peças
                    </span>
                  )}
                </div>
              )}
              
              <div className="mt-4">
                <span className="text-blue-600 hover:text-blue-800 text-sm font-medium inline-flex items-center">
                  Ver mais
                  <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                  </svg>
                </span>
              </div>
            </CardContent>
          </Card>
        </Link>
      ))}
    </div>
  );
}
