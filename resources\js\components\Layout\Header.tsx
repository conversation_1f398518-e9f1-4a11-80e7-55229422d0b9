import { Link, usePage } from '@inertiajs/react';
import { useState, useEffect } from 'react';
import { Fa<PERSON><PERSON>ch, Fa<PERSON>ser, FaBell, FaPlus, FaBars, FaTimes } from 'react-icons/fa';
import { routes } from '@/routes';

type SharedData = {
    auth: {
        user: {
            id: number;
            name: string;
            email: string;
            avatar?: string;
        } | null;
    };
};

export default function Header() {
    const { auth } = usePage<SharedData>().props;
    const [isMenuOpen, setIsMenuOpen] = useState(false);
    const [searchQuery, setSearchQuery] = useState('');
    const [isScrolled, setIsScrolled] = useState(false);

    // Efeito para adicionar sombra ao scrollar a página
    useEffect(() => {
        const handleScroll = () => {
            setIsScrolled(window.scrollY > 10);
        };

        window.addEventListener('scroll', handleScroll);
        return () => window.removeEventListener('scroll', handleScroll);
    }, []);

    const toggleMenu = () => {
        setIsMenuOpen(!isMenuOpen);
    };

    const handleSearch = (e: React.FormEvent) => {
        e.preventDefault();
        if (searchQuery.trim()) {
            window.location.href = `/anuncios?search=${encodeURIComponent(searchQuery)}`;
        }
    };

    return (
        <header 
            className={`fixed w-full z-50 transition-all duration-300 ${
                isScrolled ? 'bg-white shadow-md' : 'bg-white/90 backdrop-blur-sm'
            }`}
        >
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="flex justify-between items-center h-16">
                    {/* Logo */}
                    <div className="flex-shrink-0">
                        <Link href="/" className="flex items-center">
                            <span className="text-2xl font-bold text-orange-600">AutoMercado</span>
                        </Link>
                    </div>

                    {/* Barra de pesquisa (desktop) */}
                    <div className="hidden md:flex flex-1 max-w-2xl mx-4">
                        <form onSubmit={handleSearch} className="w-full">
                            <div className="relative">
                                <input
                                    type="text"
                                    placeholder="O que você está procurando?"
                                    className="w-full px-4 py-2 border border-gray-300 rounded-l-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                    value={searchQuery}
                                    onChange={(e) => setSearchQuery(e.target.value)}
                                />
                                <button
                                    type="submit"
                                    className="absolute right-0 top-0 h-full px-4 bg-orange-600 text-white rounded-r-md hover:bg-orange-700 focus:outline-none focus:ring-2 focus:ring-orange-500 focus:ring-offset-2"
                                >
                                    <FaSearch />
                                </button>
                            </div>
                        </form>
                    </div>

                    {/* Menu de navegação (desktop) */}
                    <nav className="hidden md:flex items-center space-x-4">
                        <Link 
                            href={routes.ads.create} 
                            className="flex items-center px-4 py-2 text-sm font-medium text-white bg-orange-600 rounded-md hover:bg-orange-700"
                        >
                            <FaPlus className="mr-2" />
                            Anunciar
                        </Link>
                        
                        {auth.user ? (
                            <div className="flex items-center space-x-4">
                                <Link 
                                    href={routes.notifications} 
                                    className="p-2 text-gray-600 hover:text-orange-600 relative"
                                >
                                    <FaBell className="text-xl" />
                                    <span className="absolute top-0 right-0 h-2 w-2 rounded-full bg-orange-600"></span>
                                </Link>
                                <Link 
                                    href={routes.profile} 
                                    className="flex items-center space-x-2 text-gray-700 hover:text-orange-600"
                                >
                                    {auth.user.avatar ? (
                                        <img 
                                            src={auth.user.avatar} 
                                            alt={auth.user.name} 
                                            className="h-8 w-8 rounded-full"
                                        />
                                    ) : (
                                        <div className="h-8 w-8 rounded-full bg-orange-100 flex items-center justify-center">
                                            <FaUser className="text-orange-600" />
                                        </div>
                                    )}
                                    <span className="hidden lg:inline">{auth.user.name.split(' ')[0]}</span>
                                </Link>
                            </div>
                        ) : (
                            <div className="flex space-x-2">
                                <Link 
                                    href={routes.login} 
                                    className="px-4 py-2 text-sm font-medium text-gray-700 hover:text-orange-600"
                                >
                                    Entrar
                                </Link>
                                <Link 
                                    href={routes.register} 
                                    className="px-4 py-2 text-sm font-medium text-orange-600 border border-orange-600 rounded-md hover:bg-orange-50"
                                >
                                    Criar conta
                                </Link>
                            </div>
                        )}
                    </nav>

                    {/* Botão do menu móvel */}
                    <div className="md:hidden">
                        <button
                            onClick={toggleMenu}
                            className="inline-flex items-center justify-center p-2 rounded-md text-gray-700 hover:text-orange-600 hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-orange-500"
                        >
                            {isMenuOpen ? <FaTimes /> : <FaBars />}
                        </button>
                    </div>
                </div>

                {/* Barra de pesquisa (mobile) */}
                <div className="md:hidden py-3">
                    <form onSubmit={handleSearch}>
                        <div className="relative">
                            <input
                                type="text"
                                placeholder="Buscar anúncios..."
                                className="w-full px-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-orange-500 focus:border-transparent"
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                            />
                            <button
                                type="submit"
                                className="absolute right-0 top-0 h-full px-4 text-gray-500 hover:text-orange-600 focus:outline-none"
                            >
                                <FaSearch />
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            {/* Menu móvel */}
            {isMenuOpen && (
                <div className="md:hidden bg-white border-t border-gray-200">
                    <div className="px-2 pt-2 pb-3 space-y-1">
                        <Link
                            href={routes.ads.index}
                            className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-orange-600 hover:bg-gray-50"
                            onClick={() => setIsMenuOpen(false)}
                        >
                            Explorar
                        </Link>
                        <Link
                            href={routes.ads.create}
                            className="flex items-center px-3 py-2 text-base font-medium text-orange-600 hover:bg-orange-50"
                            onClick={() => setIsMenuOpen(false)}
                        >
                            <FaPlus className="mr-2" />
                            Anunciar
                        </Link>
                        
                        {auth.user ? (
                            <>
                                <Link
                                    href={routes.profile}
                                    className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-orange-600 hover:bg-gray-50"
                                    onClick={() => setIsMenuOpen(false)}
                                >
                                    Meu perfil
                                </Link>
                                <Link
                                    href={routes.logout}
                                    method="post"
                                    as="button"
                                    className="w-full text-left px-3 py-2 text-base font-medium text-gray-700 hover:text-orange-600 hover:bg-gray-50"
                                    onClick={() => setIsMenuOpen(false)}
                                >
                                    Sair
                                </Link>
                            </>
                        ) : (
                            <>
                                <Link
                                    href={routes.login}
                                    className="block px-3 py-2 text-base font-medium text-gray-700 hover:text-orange-600 hover:bg-gray-50"
                                    onClick={() => setIsMenuOpen(false)}
                                >
                                    Entrar
                                </Link>
                                <Link
                                    href={routes.register}
                                    className="block px-3 py-2 text-base font-medium text-orange-600 hover:bg-orange-50"
                                    onClick={() => setIsMenuOpen(false)}
                                >
                                    Criar conta
                                </Link>
                            </>
                        )}
                    </div>
                </div>
            )}
        </header>
    );
}
