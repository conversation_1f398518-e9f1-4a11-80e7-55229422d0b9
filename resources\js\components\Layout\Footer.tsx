import { Link } from '@inertiajs/react';
import { FaFacebook, FaInstagram, FaTwitter, FaLinkedin, FaMapMarkerAlt, FaPhone, FaEnvelope } from 'react-icons/fa';

type FooterLink = {
    title: string;
    links: Array<{
        name: string;
        href: string;
        external?: boolean;
    }>;
};

const footerLinks: FooterLink[] = [
    {
        title: 'Navegação',
        links: [
            { name: '<PERSON><PERSON><PERSON>', href: '/' },
            { name: '<PERSON><PERSON><PERSON><PERSON>', href: '/anuncios' },
            { name: 'Categorias', href: '/categorias' },
            { name: 'Como funciona', href: '/como-funciona' },
            { name: 'Preç<PERSON>', href: '/planos' },
        ],
    },
    {
        title: 'Empresa',
        links: [
            { name: 'Sobre nós', href: '/sobre' },
            { name: '<PERSON><PERSON><PERSON> de uso', href: '/termos' },
            { name: 'Polí<PERSON> de privacidade', href: '/privacidade' },
            { name: 'Trabal<PERSON> conosco', href: '/trabalhe-conosco' },
            { name: '<PERSON><PERSON><PERSON>', href: '/contato' },
        ],
    },
    {
        title: 'Ajuda',
        links: [
            { name: 'Central de Ajuda', href: '/ajuda' },
            { name: 'Dúvidas frequentes', href: '/faq' },
            { name: 'Segurança', href: '/seguranca' },
            { name: 'Denunciar um problema', href: '/denunciar' },
        ],
    },
];

export default function Footer() {
    const currentYear = new Date().getFullYear();

    return (
        <footer className="bg-gray-900 text-white pt-16 pb-8">
            <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
                    {/* Sobre */}
                    <div className="col-span-1 lg:col-span-1">
                        <h3 className="text-2xl font-bold mb-4">AutoMercado</h3>
                        <p className="text-gray-400 mb-6">
                            A plataforma mais completa para compra e venda de veículos e peças automotivas.
                        </p>
                        <div className="flex space-x-4">
                            <a 
                                href="https://facebook.com" 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-gray-400 hover:text-white transition-colors"
                                aria-label="Facebook"
                            >
                                <FaFacebook className="w-6 h-6" />
                            </a>
                            <a 
                                href="https://instagram.com" 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-gray-400 hover:text-white transition-colors"
                                aria-label="Instagram"
                            >
                                <FaInstagram className="w-6 h-6" />
                            </a>
                            <a 
                                href="https://twitter.com" 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-gray-400 hover:text-white transition-colors"
                                aria-label="Twitter"
                            >
                                <FaTwitter className="w-6 h-6" />
                            </a>
                            <a 
                                href="https://linkedin.com" 
                                target="_blank" 
                                rel="noopener noreferrer"
                                className="text-gray-400 hover:text-white transition-colors"
                                aria-label="LinkedIn"
                            >
                                <FaLinkedin className="w-6 h-6" />
                            </a>
                        </div>
                    </div>

                    {/* Links de navegação */}
                    {footerLinks.map((section, index) => (
                        <div key={index} className="mt-6 md:mt-0">
                            <h4 className="text-lg font-semibold mb-4">{section.title}</h4>
                            <ul className="space-y-2">
                                {section.links.map((link, linkIndex) => (
                                    <li key={linkIndex}>
                                        {link.external ? (
                                            <a 
                                                href={link.href} 
                                                target="_blank" 
                                                rel="noopener noreferrer"
                                                className="text-gray-400 hover:text-white transition-colors"
                                            >
                                                {link.name}
                                            </a>
                                        ) : (
                                            <Link 
                                                href={link.href}
                                                className="text-gray-400 hover:text-white transition-colors"
                                            >
                                                {link.name}
                                            </Link>
                                        )}
                                    </li>
                                ))}
                            </ul>
                        </div>
                    ))}

                    {/* Contato */}
                    <div className="mt-6 md:mt-0">
                        <h4 className="text-lg font-semibold mb-4">Contato</h4>
                        <ul className="space-y-3">
                            <li className="flex items-start">
                                <FaMapMarkerAlt className="text-orange-500 mt-1 mr-3 flex-shrink-0" />
                                <span className="text-gray-400">
                                    Av. Paulista, 1000<br />
                                    São Paulo - SP, 01310-100
                                </span>
                            </li>
                            <li className="flex items-center">
                                <FaPhone className="text-orange-500 mr-3" />
                                <a href="tel:+5511999999999" className="text-gray-400 hover:text-white transition-colors">
                                    (11) 99999-9999
                                </a>
                            </li>
                            <li className="flex items-center">
                                <FaEnvelope className="text-orange-500 mr-3" />
                                <a href="mailto:<EMAIL>" className="text-gray-400 hover:text-white transition-colors">
                                    <EMAIL>
                                </a>
                            </li>
                        </ul>
                    </div>
                </div>

                <div className="border-t border-gray-800 mt-12 pt-8 flex flex-col md:flex-row justify-between items-center">
                    <p className="text-gray-400 text-sm">
                        &copy; {currentYear} AutoMercado. Todos os direitos reservados.
                    </p>
                    <div className="flex space-x-6 mt-4 md:mt-0">
                        <Link href="/termos" className="text-gray-400 hover:text-white text-sm">
                            Termos de Uso
                        </Link>
                        <Link href="/privacidade" className="text-gray-400 hover:text-white text-sm">
                            Política de Privacidade
                        </Link>
                        <Link href="/cookies" className="text-gray-400 hover:text-white text-sm">
                            Política de Cookies
                        </Link>
                    </div>
                </div>

                <div className="mt-8 text-center">
                    <p className="text-xs text-gray-500">
                        AutoMercado é uma plataforma de compra e venda de veículos e peças automotivas.
                        Não nos responsabilizamos pelos anúncios publicados por usuários.
                    </p>
                </div>
            </div>
        </footer>
    );
}
