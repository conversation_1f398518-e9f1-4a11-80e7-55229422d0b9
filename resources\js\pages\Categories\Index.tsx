import { PageProps } from '@/types';
import MainLayout from '@/layouts/MainLayout';
import { Link } from '@inertiajs/react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Category } from '@/components/Header';

interface CategoriesIndexProps extends PageProps {
    categories: Category[];
}

export default function CategoriesIndex({ categories }: CategoriesIndexProps) {
    return (
        <MainLayout>
            <div className="container mx-auto px-4 py-8">
                <h1 className="text-3xl font-bold mb-8">Todas as Categorias</h1>
                
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                    {categories.map((category) => (
                        <Link 
                            key={category.id} 
                            href={`/categorias/${category.slug}`}
                            className="block hover:opacity-90 transition-opacity"
                        >
                            <Card className="h-full">
                                <CardHeader>
                                    <div className="flex items-center space-x-4">
                                        {category.icon && (
                                            <div className="p-3 bg-primary/10 rounded-lg">
                                                <i className={`${category.icon} text-2xl text-primary`}></i>
                                            </div>
                                        )}
                                        <CardTitle className="text-xl">{category.name}</CardTitle>
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    {category.children && category.children.length > 0 && (
                                        <div className="mt-2">
                                            <h3 className="text-sm font-medium text-muted-foreground mb-2">
                                                Subcategorias:
                                            </h3>
                                            <div className="flex flex-wrap gap-2">
                                                {category.children.map((subcategory) => (
                                                    <span 
                                                        key={subcategory.id}
                                                        className="inline-flex items-center px-3 py-1 rounded-full text-xs font-medium bg-primary/10 text-primary"
                                                    >
                                                        {subcategory.name}
                                                    </span>
                                                ))}
                                            </div>
                                        </div>
                                    )}
                                </CardContent>
                            </Card>
                        </Link>
                    ))}
                </div>
            </div>
        </MainLayout>
    );
}
