import { PageProps } from '@/types';
import MainLayout from '@/layouts/MainLayout';
import { Head, Link } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Category } from '@/components/Header';

interface Listing {
    id: number;
    title: string;
    price: number;
    location: string;
    created_at: string;
    main_image?: {
        url: string;
    };
    category: {
        name: string;
    };
    brand: {
        name: string;
    };
}

interface PaginatedListings {
    data: Listing[];
    links: Array<{
        url: string | null;
        label: string;
        active: boolean;
    }>;
}

interface CategoryShowProps extends PageProps {
    category: Category;
    listings: PaginatedListings;
}

export default function CategoryShow({ category, listings }: CategoryShowProps) {
    return (
        <MainLayout>
            <Head title={category.name} />
            
            <div className="container mx-auto px-4 py-8">
                <div className="mb-8">
                    <h1 className="text-3xl font-bold mb-2">{category.name}</h1>
                    {category.description && (
                        <p className="text-muted-foreground">{category.description}</p>
                    )}
                </div>

                {category.children && category.children.length > 0 && (
                    <div className="mb-8">
                        <h2 className="text-xl font-semibold mb-4">Subcategorias</h2>
                        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
                            {category.children.map((subcategory) => (
                                <Link 
                                    key={subcategory.id}
                                    href={`/categorias/${subcategory.slug}`}
                                    className="block"
                                >
                                    <Card className="h-full hover:bg-accent/50 transition-colors">
                                        <CardHeader className="pb-2">
                                            <CardTitle className="text-lg">
                                                {subcategory.name}
                                            </CardTitle>
                                        </CardHeader>
                                        <CardContent>
                                            <Button variant="link" className="p-0 h-auto">
                                                Ver anúncios
                                            </Button>
                                        </CardContent>
                                    </Card>
                                </Link>
                            ))}
                        </div>
                    </div>
                )}

                <div>
                    <div className="flex justify-between items-center mb-6">
                        <h2 className="text-2xl font-bold">Anúncios em {category.name}</h2>
                        <div className="flex space-x-2">
                            <Button variant="outline">Ordenar por</Button>
                            <Button variant="outline">Filtrar</Button>
                        </div>
                    </div>

                    {listings.data.length > 0 ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
                            {listings.data.map((listing) => (
                                <div key={listing.id} className="border rounded-lg overflow-hidden hover:shadow-md transition-shadow">
                                    <div className="aspect-video bg-muted relative">
                                        {listing.main_image && (
                                            <img 
                                                src={listing.main_image.url} 
                                                alt={listing.title}
                                                className="w-full h-full object-cover"
                                            />
                                        )}
                                    </div>
                                    <div className="p-4">
                                        <h3 className="font-semibold text-lg mb-1">{listing.title}</h3>
                                        <p className="text-primary font-bold text-lg mb-2">
                                            {new Intl.NumberFormat('pt-BR', {
                                                style: 'currency',
                                                currency: 'BRL'
                                            }).format(listing.price)}
                                        </p>
                                        <div className="flex items-center text-sm text-muted-foreground">
                                            <span>{listing.location}</span>
                                            <span className="mx-2">•</span>
                                            <span>{new Date(listing.created_at).toLocaleDateString()}</span>
                                        </div>
                                    </div>
                                </div>
                            ))}
                        </div>
                    ) : (
                        <div className="text-center py-12">
                            <p className="text-muted-foreground mb-4">Nenhum anúncio encontrado nesta categoria.</p>
                            <Button asChild>
                                <Link href="/anunciar">Anunciar agora</Link>
                            </Button>
                        </div>
                    )}

                    {listings.links.length > 3 && (
                        <div className="mt-8 flex justify-center">
                            <nav className="flex items-center space-x-2">
                                {listings.links.map((link, index) => (
                                    <Link
                                        key={index}
                                        href={link.url || '#'}
                                        className={`px-4 py-2 rounded-md ${
                                            link.active
                                                ? 'bg-primary text-primary-foreground'
                                                : 'hover:bg-accent'
                                        } ${!link.url ? 'opacity-50 cursor-not-allowed' : ''}`}
                                        dangerouslySetInnerHTML={{ __html: link.label }}
                                    />
                                ))}
                            </nav>
                        </div>
                    )}
                </div>
            </div>
        </MainLayout>
    );
}
