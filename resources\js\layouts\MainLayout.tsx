import { ReactNode } from 'react';
import Footer from '@/components/Footer';
import Header, { Category } from '@/components/Header';

interface User {
    id: number;
    name: string;
    email: string;
}

interface MainLayoutProps {
    categories?: Category[];
    children: ReactNode;
    auth?: {
        user: User | null;
    };
    errors?: Record<string, string>;
    title?: string;
}

export default function MainLayout({ 
    categories = [], 
    children,
    title = 'VeiculosBR - Loja de Veículos'
}: MainLayoutProps) {
    return (
        <div className="min-h-screen bg-background">
            <Header categories={categories} />
            <main className="container mx-auto px-4 py-8">
                {title && (
                    <h1 className="text-3xl font-bold mb-8">{title}</h1>
                )}
                {children}
            </main>
            <Footer />
        </div>
    );
}
