<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Advertisement;
use App\Models\Vehicle;
use App\Models\User;
use App\Http\Requests\Admin\StoreAdvertisementRequest;
use App\Http\Requests\Admin\UpdateAdvertisementRequest;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Inertia\Inertia;
use Illuminate\Support\Str;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;

class AdvertisementController extends Controller
{
    use AuthorizesRequests;
    
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request)
    {
        $this->authorize('viewAny', Advertisement::class);

        $advertisements = Advertisement::with(['user', 'vehicle'])
            ->when($request->search, function ($query, $search) {
                $query->where('title', 'like', "%{$search}%")
                    ->orWhere('description', 'like', "%{$search}%");
            })
            ->when($request->status, function ($query, $status) {
                $query->where('status', $status);
            })
            ->latest()
            ->paginate(10)
            ->withQueryString();

        return Inertia::render('Admin/Advertisements/Index', [
            'advertisements' => $advertisements,
            'filters' => $request->only(['search', 'status']),
            'statuses' => [
                'draft' => 'Rascunho',
                'pending_review' => 'Aguardando Revisão',
                'approved' => 'Aprovado',
                'rejected' => 'Rejeitado',
                'published' => 'Publicado',
                'expired' => 'Expirado',
                'sold' => 'Vendido',
            ],
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $this->authorize('create', Advertisement::class);

        return Inertia::render('Admin/Advertisements/Create', [
            'vehicles' => Vehicle::select(['id', 'brand_id', 'model', 'year_manufacture'])
                ->with('brand')
                ->get(),
            'users' => User::select(['id', 'name', 'email'])->get(),
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(StoreAdvertisementRequest $request)
    {
        $this->authorize('create', Advertisement::class);

        $data = $request->validated();
        $data['slug'] = Str::slug($data['title']);
        $data['user_id'] = $request->user_id ?? Auth::id();
        
        $advertisement = Advertisement::create($data);

        // Handle image uploads
        if ($request->hasFile('images')) {
            foreach ($request->file('images') as $image) {
                $advertisement->addMedia($image)
                    ->toMediaCollection('images');
            }
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $advertisement->addMedia($request->file('featured_image'))
                ->toMediaCollection('featured_image');
        }

        return redirect()
            ->route('admin.advertisements.edit', $advertisement)
            ->with('success', 'Anúncio criado com sucesso!');
    }

    /**
     * Display the specified resource.
     */
    public function show(Advertisement $advertisement)
    {
        $this->authorize('view', $advertisement);

        $advertisement->load(['user', 'vehicle', 'vehicle.brand', 'vehicle.category']);
        
        return Inertia::render('Admin/Advertisements/Show', [
            'advertisement' => $advertisement,
        ]);
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(Advertisement $advertisement)
    {
        $this->authorize('update', $advertisement);

        $advertisement->load(['vehicle', 'user']);
        
        return Inertia::render('Admin/Advertisements/Edit', [
            'advertisement' => $advertisement,
            'vehicles' => Vehicle::select(['id', 'brand_id', 'model', 'year_manufacture'])
                ->with('brand')
                ->get(),
            'users' => User::select(['id', 'name', 'email'])->get(),
            'statuses' => [
                'draft' => 'Rascunho',
                'pending_review' => 'Aguardando Revisão',
                'approved' => 'Aprovado',
                'rejected' => 'Rejeitado',
                'published' => 'Publicado',
                'expired' => 'Expirado',
                'sold' => 'Vendido',
            ],
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(UpdateAdvertisementRequest $request, Advertisement $advertisement)
    {
        $this->authorize('update', $advertisement);

        $data = $request->validated();
        $data['slug'] = Str::slug($data['title']);
        
        $advertisement->update($data);

        // Handle image uploads
        if ($request->hasFile('images')) {
            $advertisement->clearMediaCollection('images');
            
            foreach ($request->file('images') as $image) {
                $advertisement->addMedia($image)
                    ->toMediaCollection('images');
            }
        }

        // Handle featured image upload
        if ($request->hasFile('featured_image')) {
            $advertisement->clearMediaCollection('featured_image');
            $advertisement->addMedia($request->file('featured_image'))
                ->toMediaCollection('featured_image');
        }

        return redirect()
            ->route('admin.advertisements.edit', $advertisement)
            ->with('success', 'Anúncio atualizado com sucesso!');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(Advertisement $advertisement)
    {
        $this->authorize('delete', $advertisement);

        $advertisement->delete();

        return redirect()
            ->route('admin.advertisements.index')
            ->with('success', 'Anúncio excluído com sucesso!');
    }

    /**
     * Approve the specified advertisement.
     */
    public function approve(Request $request, Advertisement $advertisement)
    {
        $this->authorize('approve', $advertisement);

        $advertisement->update([
            'status' => Advertisement::STATUS_APPROVED,
            'rejection_reason' => null,
        ]);

        return back()->with('success', 'Anúncio aprovado com sucesso!');
    }

    /**
     * Reject the specified advertisement.
     */
    public function reject(Request $request, Advertisement $advertisement)
    {
        $this->authorize('reject', $advertisement);

        $request->validate([
            'rejection_reason' => 'required|string|max:1000',
        ]);

        $advertisement->update([
            'status' => Advertisement::STATUS_REJECTED,
            'rejection_reason' => $request->rejection_reason,
        ]);

        return back()->with('success', 'Anúncio rejeitado com sucesso!');
    }

    /**
     * Publish the specified advertisement.
     */
    public function publish(Request $request, Advertisement $advertisement)
    {
        $this->authorize('publish', $advertisement);

        $advertisement->update([
            'status' => Advertisement::STATUS_PUBLISHED,
            'published_at' => now(),
            'expires_at' => now()->addDays(30), // 30 days expiration
        ]);

        return back()->with('success', 'Anúncio publicado com sucesso!');
    }

    /**
     * Mark the specified advertisement as sold.
     */
    public function markAsSold(Request $request, Advertisement $advertisement)
    {
        $this->authorize('markAsSold', $advertisement);

        $advertisement->update([
            'status' => Advertisement::STATUS_SOLD,
        ]);

        return back()->with('success', 'Anúncio marcado como vendido com sucesso!');
    }
}
