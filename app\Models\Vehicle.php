<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\MediaLibrary\HasMedia;
use Spatie\MediaLibrary\InteractsWithMedia;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Builder;

class Vehicle extends Model implements HasMedia
{
    use SoftDeletes, InteractsWithMedia;

    /**
     * The attributes that are searchable.
     *
     * @var array
     */
    protected $searchable = [
        'model',
        'description',
        'color',
        'fuel_type',
        'transmission',
        'status',
    ];

    /**
     * The attributes that are mass assignable.
     *
     * @var array
     */
    protected $fillable = [
        'user_id',
        'category_id',
        'brand_id',
        'model',
        'slug',
        'year_manufacture',
        'model_year',
        'license_plate',
        'chassis_number',
        'color',
        'fuel_type',
        'transmission',
        'mileage',
        'description',
        'price',
        'promotional_price',
        'is_negotiable',
        'is_featured',
        'status',
        'views',
        'seo_title',
        'seo_description',
        'seo_keywords',
    ];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'price' => 'decimal:2',
        'promotional_price' => 'decimal:2',
        'is_negotiable' => 'boolean',
        'is_featured' => 'boolean',
        'views' => 'integer',
        'year_manufacture' => 'integer',
        'model_year' => 'integer',
        'mileage' => 'integer',
    ];

    /**
     * The model's default values for attributes.
     *
     * @var array
     */
    protected $attributes = [
        'status' => 'draft',
        'is_negotiable' => false,
        'is_featured' => false,
        'views' => 0,
    ];

    /**
     * Get the user that owns the vehicle.
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * Get the category that owns the vehicle.
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

    /**
     * Get the brand that owns the vehicle.
     */
    public function brand(): BelongsTo
    {
        return $this->belongsTo(Brand::class);
    }

    /**
     * Get the images for the vehicle.
     */
    public function images(): MorphMany
    {
        return $this->morphMany(Image::class, 'imageable');
    }

    /**
     * Scope a query to filter vehicles.
     */
    public function scopeFilter(Builder $query, array $filters): void
    {
        $query->when($filters['search'] ?? null, function (Builder $query, $search) {
            $query->where(function (Builder $query) use ($search) {
                foreach ($this->searchable as $column) {
                    $query->orWhere($column, 'like', "%{$search}%");
                }
            });
        })->when($filters['brand'] ?? null, function (Builder $query, $brand) {
            $query->where('brand_id', $brand);
        })->when($filters['category'] ?? null, function (Builder $query, $category) {
            $query->where('category_id', $category);
        })->when($filters['status'] ?? null, function (Builder $query, $status) {
            $query->where('status', $status);
        });
    }

    /**
     * Get the main image of the vehicle.
     */
    public function mainImage()
    {
        return $this->morphOne(Image::class, 'imageable')
            ->where('is_main', true);
    }

    /**
     * The features that belong to the vehicle.
     */
    public function features(): BelongsToMany
    {
        return $this->belongsToMany(Feature::class, 'vehicle_feature')
            ->withPivot('value')
            ->withTimestamps();
    }

    /**
     * The parts that are compatible with this vehicle.
     */
    public function compatibleParts(): BelongsToMany
    {
        return $this->belongsToMany(Part::class, 'part_vehicle')
            ->withPivot('notes')
            ->withTimestamps();
    }

    /**
     * Scope a query to only include published vehicles.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopePublished($query)
    {
        return $query->where('status', 'published');
    }

    /**
     * Scope a query to only include featured vehicles.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * Get the route key for the model.
     *
     * @return string
     */
    public function getRouteKeyName()
    {
        return 'slug';
    }

    /**
     * Increment the view count for the vehicle.
     *
     * @return void
     */
    public function incrementViewCount()
    {
        $this->increment('views');
    }

    /**
     * Get the final price of the vehicle (considers promotional price if available).
     *
     * @return float
     */
    public function getFinalPriceAttribute(): float
    {
        return $this->promotional_price ?? $this->price;
    }

    /**
     * Check if the vehicle has a promotional price.
     *
     * @return bool
     */
    public function hasPromotion(): bool
    {
        return !is_null($this->promotional_price);
    }

    /**
     * Scope a query to only include vehicles of a specific category.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $categoryId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * Scope a query to only include vehicles of a specific brand.
     *
     * @param  \Illuminate\Database\Eloquent\Builder  $query
     * @param  int  $brandId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeOfBrand($query, $brandId)
    {
        return $query->where('brand_id', $brandId);
    }

    /**
     * Get the URL for the vehicle.
     *
     * @return string
     */
    public function getUrlAttribute()
    {
        return route('vehicles.show', $this->slug);
    }

    /**
     * Get the formatted price.
     *
     * @return string
     */
    public function getFormattedPriceAttribute()
    {
        return 'R$ ' . number_format($this->price, 2, ',', '.');
    }

    /**
     * Get the formatted promotional price.
     *
     * @return string|null
     */
    public function getFormattedPromotionalPriceAttribute()
    {
        return $this->promotional_price ? 'R$ ' . number_format($this->promotional_price, 2, ',', '.') : null;
    }

    /**
     * Get the vehicle's full name (brand + model + year).
     *
     * @return string
     */
    public function getFullNameAttribute()
    {
        return "{$this->brand->name} {$this->model} {$this->year_manufacture}";
    }

    /**
     * Register media collections for the vehicle.
     */
    public function registerMediaCollections(): void
    {
        $this->addMediaCollection('images')
            ->useDisk('public')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp'])
            ->maxNumberOfFiles(20)
            ->onlyKeepLatest(20);

        $this->addMediaCollection('featured_image')
            ->singleFile()
            ->useDisk('public')
            ->acceptsMimeTypes(['image/jpeg', 'image/png', 'image/webp']);
    }

    /**
     * Register media conversions for the vehicle.
     */
    public function registerMediaConversions($media = null): void
    {
        $this->addMediaConversion('thumb')
            ->width(300)
            ->height(200)
            ->sharpen(10)
            ->optimize()
            ->nonQueued();

        $this->addMediaConversion('medium')
            ->width(800)
            ->height(600)
            ->quality(85)
            ->optimize();

        $this->addMediaConversion('large')
            ->width(1920)
            ->height(1080)
            ->quality(90)
            ->optimize();
    }

    /**
     * Get the main image URL.
     */
    public function getMainImageUrlAttribute(): ?string
    {
        $media = $this->getFirstMedia('featured_image');
        
        if (!$media) {
            $media = $this->getFirstMedia('images');
        }
        
        return $media ? $media->getUrl('medium') : null;
    }

    /**
     * Get all image URLs for the vehicle.
     */
    public function getAllImageUrlsAttribute(): array
    {
        return $this->getMedia('images')
            ->map(function ($media) {
                return [
                    'thumb' => $media->getUrl('thumb'),
                    'medium' => $media->getUrl('medium'),
                    'large' => $media->getUrl('large'),
                    'original' => $media->getUrl(),
                ];
            })
            ->toArray();
    }
}
