import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { PageProps } from '@/types';
import MainLayout from '@/Layouts/MainLayout';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Badge } from '@/components/ui/badge';
import { ArrowLeft, Phone, MessageSquare, Share2, Heart, MapPin, CheckCircle, Clock, Zap, Shield, Star } from 'lucide-react';
import { formatCurrency } from '@/lib/utils';

export default function VehicleShow({ vehicle, relatedVehicles }: PageProps<{ vehicle: any, relatedVehicles: any[] }>) {
    const mainImage = vehicle.images?.find((img: any) => img.is_main) || vehicle.images?.[0];
    const otherImages = vehicle.images?.filter((img: any) => img !== mainImage) || [];

    const features = [
        { icon: <CheckCircle className="w-5 h-5 text-green-500" />, label: 'Documentação em dia' },
        { icon: <Clock className="w-5 h-5 text-blue-500" />, label: 'Revisões em dia' },
        { icon: <Shield className="w-5 h-5 text-purple-500" />, label: 'Garantia de fábrica' },
        { icon: <Zap className="w-5 h-5 text-yellow-500" />, label: 'Único dono' },
    ];

    return (
        <MainLayout>
            <Head title={`${vehicle.brand.name} ${vehicle.model} ${vehicle.year_manufacture}`} />
            
            <div className="container py-8">
                {/* Voltar */}
                <Button variant="ghost" asChild className="mb-6">
                    <Link href={route('vehicles.index')}>
                        <ArrowLeft className="w-4 h-4 mr-2" />
                        Voltar para a lista de veículos
                    </Link>
                </Button>

                <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                    {/* Conteúdo Principal */}
                    <div className="lg:col-span-2 space-y-6">
                        {/* Galeria de Imagens */}
                        <Card>
                            <CardContent className="p-0">
                                <div className="relative aspect-video bg-muted rounded-t-lg overflow-hidden">
                                    {mainImage ? (
                                        <img 
                                            src={mainImage.url} 
                                            alt={`${vehicle.brand.name} ${vehicle.model}`}
                                            className="w-full h-full object-cover"
                                        />
                                    ) : (
                                        <div className="w-full h-full flex items-center justify-center bg-muted">
                                            <span className="text-muted-foreground">Sem imagem</span>
                                        </div>
                                    )}
                                    <div className="absolute top-4 right-4 flex gap-2">
                                        <Button variant="outline" size="icon" className="rounded-full bg-background/80 backdrop-blur-sm">
                                            <Share2 className="w-4 h-4" />
                                            <span className="sr-only">Compartilhar</span>
                                        </Button>
                                        <Button variant="outline" size="icon" className="rounded-full bg-background/80 backdrop-blur-sm">
                                            <Heart className="w-4 h-4" />
                                            <span className="sr-only">Favoritar</span>
                                        </Button>
                                    </div>
                                </div>
                                
                                {otherImages.length > 0 && (
                                    <div className="p-4 flex gap-2 overflow-x-auto">
                                        {otherImages.map((image: any) => (
                                            <button 
                                                key={image.id}
                                                className="flex-shrink-0 w-20 h-16 rounded-md overflow-hidden border"
                                            >
                                                <img 
                                                    src={image.url} 
                                                    alt={`${vehicle.brand.name} ${vehicle.model}`}
                                                    className="w-full h-full object-cover"
                                                />
                                            </button>
                                        ))}
                                    </div>
                                )}
                            </CardContent>
                        </Card>

                        {/* Informações do Veículo */}
                        <Card>
                            <CardHeader>
                                <div className="flex justify-between items-start">
                                    <div>
                                        <div className="flex items-center gap-2">
                                            <h1 className="text-2xl font-bold">
                                                {vehicle.brand.name} {vehicle.model}
                                            </h1>
                                            <Badge variant="outline" className="text-sm">
                                                {vehicle.year_manufacture}/{vehicle.model_year}
                                            </Badge>
                                        </div>
                                        <div className="flex items-center text-muted-foreground mt-1">
                                            <MapPin className="w-4 h-4 mr-1" />
                                            <span>Cidade, Estado</span>
                                        </div>
                                    </div>
                                    <div className="text-right">
                                        <div className="text-2xl font-bold text-primary">
                                            {vehicle.promotional_price ? (
                                                <>
                                                    <span className="line-through text-muted-foreground text-lg mr-2">
                                                        {formatCurrency(vehicle.price)}
                                                    </span>
                                                    {formatCurrency(vehicle.promotional_price)}
                                                </>
                                            ) : (
                                                formatCurrency(vehicle.price)
                                            )}
                                        </div>
                                        {vehicle.is_negotiable && (
                                            <div className="text-sm text-green-600">Aceita troca</div>
                                        )}
                                    </div>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <Tabs defaultValue="details" className="w-full">
                                    <TabsList className="grid w-full grid-cols-3">
                                        <TabsTrigger value="details">Detalhes</TabsTrigger>
                                        <TabsTrigger value="specs">Especificações</TabsTrigger>
                                        <TabsTrigger value="seller">Vendedor</TabsTrigger>
                                    </TabsList>
                                    
                                    <TabsContent value="details" className="pt-6">
                                        <h3 className="text-lg font-semibold mb-4">Descrição</h3>
                                        <p className="text-muted-foreground">
                                            {vehicle.description || 'Nenhuma descrição fornecida.'}
                                        </p>
                                        
                                        <h3 className="text-lg font-semibold mt-8 mb-4">Destaques</h3>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            {features.map((feature, index) => (
                                                <div key={index} className="flex items-start gap-3">
                                                    {feature.icon}
                                                    <span>{feature.label}</span>
                                                </div>
                                            ))}
                                        </div>
                                    </TabsContent>
                                    
                                    <TabsContent value="specs" className="pt-6">
                                        <h3 className="text-lg font-semibold mb-4">Especificações Técnicas</h3>
                                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                                            <div className="space-y-2">
                                                <div className="flex justify-between">
                                                    <span className="text-muted-foreground">Marca</span>
                                                    <span className="font-medium">{vehicle.brand.name}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-muted-foreground">Modelo</span>
                                                    <span className="font-medium">{vehicle.model}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-muted-foreground">Ano</span>
                                                    <span className="font-medium">{vehicle.year_manufacture}/{vehicle.model_year}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-muted-foreground">Quilometragem</span>
                                                    <span className="font-medium">
                                                        {parseInt(vehicle.mileage).toLocaleString('pt-BR')} km
                                                    </span>
                                                </div>
                                            </div>
                                            <div className="space-y-2">
                                                <div className="flex justify-between">
                                                    <span className="text-muted-foreground">Cor</span>
                                                    <span className="font-medium">{vehicle.color || 'Não informada'}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-muted-foreground">Combustível</span>
                                                    <span className="font-medium capitalize">{vehicle.fuel_type}</span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-muted-foreground">Câmbio</span>
                                                    <span className="font-medium capitalize">
                                                        {vehicle.transmission === 'manual' ? 'Manual' : 
                                                         vehicle.transmission === 'automatic' ? 'Automático' : 
                                                         vehicle.transmission === 'semi_automatic' ? 'Semi-automático' : 
                                                         vehicle.transmission === 'cvt' ? 'CVT' : vehicle.transmission}
                                                    </span>
                                                </div>
                                                <div className="flex justify-between">
                                                    <span className="text-muted-foreground">Placa</span>
                                                    <span className="font-medium">
                                                        {vehicle.license_plate || 'Não informada'}
                                                    </span>
                                                </div>
                                            </div>
                                        </div>
                                    </TabsContent>
                                    
                                    <TabsContent value="seller" className="pt-6">
                                        <div className="flex items-start gap-4">
                                            <div className="w-16 h-16 rounded-full bg-muted flex items-center justify-center">
                                                <span className="text-2xl font-bold text-muted-foreground">
                                                    {vehicle.user.name.charAt(0).toUpperCase()}
                                                </span>
                                            </div>
                                            <div>
                                                <h4 className="font-semibold">{vehicle.user.name}</h4>
                                                <div className="flex items-center text-muted-foreground text-sm">
                                                    <Star className="w-4 h-4 text-yellow-500 fill-yellow-500 mr-1" />
                                                    <span>4.8 (32 avaliações)</span>
                                                </div>
                                                <p className="text-sm mt-1">
                                                    Anunciante desde {new Date(vehicle.user.created_at).getFullYear()}
                                                </p>
                                            </div>
                                        </div>
                                        
                                        <div className="mt-6 space-y-4">
                                            <Button className="w-full" size="lg">
                                                <Phone className="w-4 h-4 mr-2" />
                                                Ver telefone
                                            </Button>
                                            <Button variant="outline" className="w-full" size="lg">
                                                <MessageSquare className="w-4 h-4 mr-2" />
                                                Enviar mensagem
                                            </Button>
                                        </div>
                                    </TabsContent>
                                </Tabs>
                            </CardContent>
                        </Card>
                    </div>
                    
                    {/* Barra Lateral */}
                    <div className="space-y-6">
                        {/* Contato */}
                        <Card>
                            <CardHeader>
                                <CardTitle>Entre em contato</CardTitle>
                            </CardHeader>
                            <CardContent className="space-y-4">
                                <Button className="w-full" size="lg">
                                    <Phone className="w-4 h-4 mr-2" />
                                    Ver telefone
                                </Button>
                                <Button variant="outline" className="w-full" size="lg">
                                    <MessageSquare className="w-4 h-4 mr-2" />
                                    Enviar mensagem
                                </Button>
                                
                                <div className="pt-4 border-t">
                                    <h4 className="font-medium mb-2">Formas de pagamento</h4>
                                    <div className="grid grid-cols-3 gap-2 text-sm text-muted-foreground">
                                        <span>• À vista</span>
                                        <span>• Financiamento</span>
                                        <span>• Consórcio</span>
                                        <span>• Cartão de crédito</span>
                                        <span>• Boleto</span>
                                        <span>• PIX</span>
                                    </div>
                                </div>
                            </CardContent>
                        </Card>
                        
                        {/* Veículos Relacionados */}
                        {relatedVehicles.length > 0 && (
                            <Card>
                                <CardHeader>
                                    <CardTitle>Veículos semelhantes</CardTitle>
                                </CardHeader>
                                <CardContent className="space-y-4">
                                    {relatedVehicles.map((related) => {
                                        const relatedImage = related.images?.[0];
                                        return (
                                            <Link 
                                                key={related.id} 
                                                href={route('vehicles.show', related.slug)}
                                                className="flex gap-3 group"
                                            >
                                                <div className="w-20 h-16 flex-shrink-0 rounded-md overflow-hidden bg-muted">
                                                    {relatedImage ? (
                                                        <img 
                                                            src={relatedImage.url}
                                                            alt={`${related.brand.name} ${related.model}`}
                                                            className="w-full h-full object-cover group-hover:scale-105 transition-transform"
                                                        />
                                                    ) : (
                                                        <div className="w-full h-full flex items-center justify-center">
                                                            <span className="text-xs text-muted-foreground">Sem imagem</span>
                                                        </div>
                                                    )}
                                                </div>
                                                <div>
                                                    <h4 className="font-medium group-hover:text-primary transition-colors">
                                                        {related.brand.name} {related.model}
                                                    </h4>
                                                    <div className="text-sm text-muted-foreground">
                                                        {related.year_manufacture} • {parseInt(related.mileage).toLocaleString('pt-BR')} km
                                                    </div>
                                                    <div className="font-semibold text-sm">
                                                        {related.promotional_price ? (
                                                            <>
                                                                <span className="line-through text-muted-foreground text-xs mr-1">
                                                                    {formatCurrency(related.price)}
                                                                </span>
                                                                <span className="text-red-600">
                                                                    {formatCurrency(related.promotional_price)}
                                                                </span>
                                                            </>
                                                        ) : (
                                                            formatCurrency(related.price)
                                                        )}
                                                    </div>
                                                </div>
                                            </Link>
                                        );
                                    })}
                                </CardContent>
                            </Card>
                        )}
                    </div>
                </div>
            </div>
        </MainLayout>
    );
}
