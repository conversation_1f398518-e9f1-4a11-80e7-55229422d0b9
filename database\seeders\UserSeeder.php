<?php

namespace Database\Seeders;

use App\Models\User;
use App\Models\Address;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class UserSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Criar usuário administrador
        $admin = User::create([
            'name' => 'Administrador',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '(11) 99999-9999',
            'cpf_cnpj' => '123.456.789-00',
            'type' => 'individual',
            'birth_date' => '1990-01-01',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);

        // Criar endereço para o administrador
        $admin->addresses()->create([
            'type' => 'main',
            'street' => 'Rua Exemplo',
            'number' => '123',
            'complement' => 'Sala 101',
            'neighborhood' => 'Centro',
            'city' => 'São Paulo',
            'state' => 'SP',
            'zip_code' => '01001-000',
            'is_default' => true,
        ]);

        // Criar usuário vendedor
        $seller = User::create([
            'name' => 'Vendedor',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '(11) 98888-8888',
            'cpf_cnpj' => '987.654.321-00',
            'type' => 'individual',
            'birth_date' => '1985-05-15',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);

        // Criar endereço para o vendedor
        $seller->addresses()->create([
            'type' => 'main',
            'street' => 'Avenida Paulista',
            'number' => '1000',
            'complement' => 'Conjunto 101',
            'neighborhood' => 'Bela Vista',
            'city' => 'São Paulo',
            'state' => 'SP',
            'zip_code' => '01310-100',
            'is_default' => true,
        ]);

        // Criar usuário cliente
        $customer = User::create([
            'name' => 'Cliente',
            'email' => '<EMAIL>',
            'password' => Hash::make('password'),
            'phone' => '(11) 97777-7777',
            'cpf_cnpj' => '456.789.123-00',
            'type' => 'individual',
            'birth_date' => '1995-10-20',
            'status' => 'active',
            'email_verified_at' => now(),
        ]);

        // Criar endereço para o cliente
        $customer->addresses()->create([
            'type' => 'main',
            'street' => 'Rua das Flores',
            'number' => '500',
            'complement' => 'Apto 302',
            'neighborhood' => 'Jardim Paulista',
            'city' => 'São Paulo',
            'state' => 'SP',
            'zip_code' => '01410-001',
            'is_default' => true,
        ]);
    }
}
