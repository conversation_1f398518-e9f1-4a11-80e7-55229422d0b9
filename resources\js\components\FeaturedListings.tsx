import { Link } from '@inertiajs/react';
import { Card, CardContent } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';

interface Vehicle {
  id: number;
  model: string;
  slug: string;
  price: number;
  promotional_price?: number;
  year_manufacture: number;
  mileage: number;
  color: string;
  fuel_type: string;
  transmission: string;
  is_featured: boolean;
  is_negotiable: boolean;
  main_image_url?: string;
  url: string;
  brand: {
    name: string;
  };
  category: {
    name: string;
    slug: string;
  };
}

interface Part {
  id: number;
  name: string;
  slug: string;
  price: number;
  promotional_price?: number;
  stock_quantity: number;
  is_original: boolean;
  is_featured: boolean;
  main_image_url?: string;
  url: string;
  brand: {
    name: string;
  };
}

interface FeaturedListingsProps {
  vehicles?: Vehicle[];
  parts?: Part[];
  title?: string;
  showMoreLink?: string;
}

export default function FeaturedListings({ 
  vehicles = [], 
  parts = [], 
  title = "Destaques",
  showMoreLink 
}: FeaturedListingsProps) {
  const formatPrice = (price: number) => {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(price);
  };

  const getFinalPrice = (item: Vehicle | Part) => {
    return item.promotional_price || item.price;
  };

  const getMainImage = (item: Vehicle | Part) => {
    if (item.main_image_url) {
      return item.main_image_url;
    }
    
    // Imagens placeholder baseadas no tipo
    if ('model' in item) {
      // É um veículo
      const vehicleImages = [
        '/toyota-corolla-2023-white-car.jpg',
        '/honda-civic-2022-silver-car.jpg',
        '/placeholder.jpg'
      ];
      return vehicleImages[Math.floor(Math.random() * vehicleImages.length)];
    } else {
      // É uma peça
      return '/car-suspension-kit-parts.jpg';
    }
  };

  const allItems = [
    ...vehicles.map(v => ({ ...v, type: 'vehicle' as const })),
    ...parts.map(p => ({ ...p, type: 'part' as const }))
  ];

  return (
    <section className="py-12">
      <div className="container mx-auto px-4">
        <div className="flex justify-between items-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900">{title}</h2>
          {showMoreLink && (
            <Link 
              href={showMoreLink}
              className="text-blue-600 hover:text-blue-800 font-medium inline-flex items-center"
            >
              Ver todos
              <svg className="w-4 h-4 ml-1" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {allItems.slice(0, 8).map((item) => (
            <Link key={`${item.type}-${item.id}`} href={item.url}>
              <Card className="group hover:shadow-lg transition-all duration-300 cursor-pointer overflow-hidden">
                <div className="aspect-video bg-gray-100 relative overflow-hidden">
                  <img
                    src={getMainImage(item)}
                    alt={'model' in item ? item.model : item.name}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-300"
                  />
                  
                  {/* Badges */}
                  <div className="absolute top-3 left-3 flex flex-col gap-2">
                    {item.is_featured && (
                      <Badge className="bg-yellow-500 hover:bg-yellow-600 text-white">
                        ⭐ Destaque
                      </Badge>
                    )}
                    {item.promotional_price && (
                      <Badge className="bg-green-500 hover:bg-green-600 text-white">
                        🏷️ Oferta
                      </Badge>
                    )}
                    {'is_original' in item && item.is_original && (
                      <Badge className="bg-blue-500 hover:bg-blue-600 text-white">
                        ✅ Original
                      </Badge>
                    )}
                  </div>

                  {/* Preço sobreposto */}
                  <div className="absolute bottom-3 right-3">
                    <div className="bg-black bg-opacity-75 text-white px-3 py-1 rounded-lg">
                      <span className="text-lg font-bold">
                        {formatPrice(getFinalPrice(item))}
                      </span>
                      {item.promotional_price && (
                        <div className="text-xs line-through opacity-75">
                          {formatPrice(item.price)}
                        </div>
                      )}
                    </div>
                  </div>
                </div>

                <CardContent className="p-4">
                  <div className="flex justify-between items-start mb-2">
                    <h3 className="text-lg font-semibold text-gray-900 line-clamp-1">
                      {'model' in item ? `${item.brand.name} ${item.model}` : item.name}
                    </h3>
                    <Badge variant="outline" className="text-xs">
                      {'model' in item ? item.category.name : 'Peça'}
                    </Badge>
                  </div>

                  {/* Detalhes específicos por tipo */}
                  {'model' in item ? (
                    // Veículo
                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex justify-between">
                        <span>Ano:</span>
                        <span className="font-medium">{item.year_manufacture}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Quilometragem:</span>
                        <span className="font-medium">{item.mileage.toLocaleString('pt-BR')} km</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Combustível:</span>
                        <span className="font-medium">{item.fuel_type}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Câmbio:</span>
                        <span className="font-medium">{item.transmission}</span>
                      </div>
                      {item.is_negotiable && (
                        <div className="text-blue-600 font-medium text-sm">
                          💰 Preço negociável
                        </div>
                      )}
                    </div>
                  ) : (
                    // Peça
                    <div className="space-y-2 text-sm text-gray-600">
                      <div className="flex justify-between">
                        <span>Marca:</span>
                        <span className="font-medium">{item.brand.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span>Estoque:</span>
                        <span className={`font-medium ${item.stock_quantity > 10 ? 'text-green-600' : 'text-orange-600'}`}>
                          {item.stock_quantity} unidades
                        </span>
                      </div>
                      <div className="text-xs text-gray-500">
                        {item.is_original ? 'Peça original' : 'Peça compatível'}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            </Link>
          ))}
        </div>

        {allItems.length === 0 && (
          <div className="text-center py-12">
            <div className="text-gray-400 text-6xl mb-4">📦</div>
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              Nenhum item em destaque no momento
            </h3>
            <p className="text-gray-600">
              Volte em breve para ver novas ofertas e produtos em destaque.
            </p>
          </div>
        )}
      </div>
    </section>
  );
}
