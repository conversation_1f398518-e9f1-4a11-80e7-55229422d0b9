import Link from "next/link"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Facebook, Instagram, Twitter, Youtube } from "lucide-react"

export function Footer() {
  return (
    <footer className="bg-primary text-primary-foreground">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand */}
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <div className="h-8 w-8 rounded-lg bg-primary-foreground flex items-center justify-center">
                <span className="text-primary font-bold text-lg">V</span>
              </div>
              <span className="font-bold text-xl">VeiculosBR</span>
            </div>
            <p className="text-primary-foreground/80 mb-4">
              A maior plataforma de veículos do Brasil. Compre, venda e alugue com segurança.
            </p>
            <div className="flex space-x-4">
              <Button variant="ghost" size="sm" className="text-primary-foreground hover:bg-primary-foreground/10">
                <Facebook className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" className="text-primary-foreground hover:bg-primary-foreground/10">
                <Instagram className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" className="text-primary-foreground hover:bg-primary-foreground/10">
                <Twitter className="h-4 w-4" />
              </Button>
              <Button variant="ghost" size="sm" className="text-primary-foreground hover:bg-primary-foreground/10">
                <Youtube className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Categories */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Categorias</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/carros"
                  className="text-primary-foreground/80 hover:text-primary-foreground transition-colors"
                >
                  Carros
                </Link>
              </li>
              <li>
                <Link
                  href="/motos"
                  className="text-primary-foreground/80 hover:text-primary-foreground transition-colors"
                >
                  Motos
                </Link>
              </li>
              <li>
                <Link
                  href="/pecas"
                  className="text-primary-foreground/80 hover:text-primary-foreground transition-colors"
                >
                  Peças
                </Link>
              </li>
              <li>
                <Link
                  href="/caminhoes"
                  className="text-primary-foreground/80 hover:text-primary-foreground transition-colors"
                >
                  Caminhões
                </Link>
              </li>
              <li>
                <Link
                  href="/eletricos"
                  className="text-primary-foreground/80 hover:text-primary-foreground transition-colors"
                >
                  Elétricos
                </Link>
              </li>
            </ul>
          </div>

          {/* Support */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Suporte</h3>
            <ul className="space-y-2">
              <li>
                <Link
                  href="/ajuda"
                  className="text-primary-foreground/80 hover:text-primary-foreground transition-colors"
                >
                  Central de Ajuda
                </Link>
              </li>
              <li>
                <Link
                  href="/contato"
                  className="text-primary-foreground/80 hover:text-primary-foreground transition-colors"
                >
                  Contato
                </Link>
              </li>
              <li>
                <Link
                  href="/seguranca"
                  className="text-primary-foreground/80 hover:text-primary-foreground transition-colors"
                >
                  Dicas de Segurança
                </Link>
              </li>
              <li>
                <Link
                  href="/termos"
                  className="text-primary-foreground/80 hover:text-primary-foreground transition-colors"
                >
                  Termos de Uso
                </Link>
              </li>
              <li>
                <Link
                  href="/privacidade"
                  className="text-primary-foreground/80 hover:text-primary-foreground transition-colors"
                >
                  Privacidade
                </Link>
              </li>
            </ul>
          </div>

          {/* Newsletter */}
          <div>
            <h3 className="font-semibold text-lg mb-4">Newsletter</h3>
            <p className="text-primary-foreground/80 mb-4">Receba as melhores ofertas no seu email</p>
            <div className="space-y-2">
              <Input
                placeholder="Seu email"
                className="bg-primary-foreground/10 border-primary-foreground/20 text-primary-foreground placeholder:text-primary-foreground/60"
              />
              <Button variant="secondary" className="w-full">
                Inscrever-se
              </Button>
            </div>
          </div>
        </div>

        <div className="border-t border-primary-foreground/20 mt-8 pt-8 text-center">
          <p className="text-primary-foreground/80">© 2024 VeiculosBR. Todos os direitos reservados.</p>
        </div>
      </div>
    </footer>
  )
}
