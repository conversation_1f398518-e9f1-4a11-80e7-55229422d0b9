<?php

namespace App\Http\Controllers\Public;

use App\Http\Controllers\Controller;
use App\Models\Category;
use App\Models\Vehicle;
use Inertia\Inertia;

class WelcomeController extends Controller
{
    /**
     * Exibe a página inicial.
     */
    public function index()
    {
        // Carrega as categorias ativas
        $categories = Category::where('is_active', true)
            ->whereNull('parent_id')
            ->with(['children' => function($query) {
                $query->where('is_active', true);
            }])
            ->orderBy('order')
            ->get(['id', 'name', 'slug', 'icon']);

        // Busca os veículos em destaque
        $featuredListings = Vehicle::with(['mainImage', 'category', 'brand'])
            ->where('is_featured', true)
            ->where('status', 'published')
            ->latest()
            ->take(8)
            ->get()
            ->map(function ($vehicle) {
                return [
                    'id' => $vehicle->id,
                    'title' => $vehicle->title,
                    'price' => (float) $vehicle->price,
                    'location' => $vehicle->city . ' - ' . $vehicle->state,
                    'created_at' => $vehicle->created_at->toDateTimeString(),
                    'main_image' => $vehicle->mainImage ? [
                        'url' => $vehicle->mainImage->getImageUrl()
                    ] : null,
                    'category' => [
                        'name' => $vehicle->category->name ?? 'Sem categoria',
                    ],
                    'brand' => [
                        'name' => $vehicle->brand->name ?? 'Sem marca',
                    ],
                    'is_featured' => $vehicle->is_featured,
                ];
            });

        return Inertia::render('Welcome', [
            'categories' => $categories,
            'featuredListings' => $featuredListings,
        ]);
    }
}
