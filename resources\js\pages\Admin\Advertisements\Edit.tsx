import { Head } from '@inertiajs/react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from '@/components/ui/card';
import { ArrowLeft } from 'lucide-react';
import { Link } from '@inertiajs/react';
import AdvertisementForm from './Form';
import AdminLayout from '@/components/Layout/AdminLayout';

export default function AdvertisementEdit({ advertisement, vehicles, users, statuses }) {
  return (
    <AdminLayout>
      <Head title={`Editar Anúncio: ${advertisement.title}`} />
      
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <Link 
              href={route('admin.advertisements.index')} 
              className="flex items-center text-sm text-muted-foreground hover:text-foreground"
            >
              <ArrowLeft className="h-4 w-4 mr-1" /> Voltar para a lista
            </Link>
            <h1 className="text-2xl font-bold tracking-tight mt-2">
              Editar An<PERSON>cio: <span className="text-muted-foreground">{advertisement.title}</span>
            </h1>
          </div>
          
          <div className="flex items-center gap-2">
            <Link href={route('admin.advertisements.show', advertisement.id)}>
              <Button variant="outline" size="sm">
                Visualizar
              </Button>
            </Link>
          </div>
        </div>
        
        <Card>
          <CardHeader>
            <CardTitle>Editar Anúncio</CardTitle>
            <CardDescription>
              Atualize as informações do anúncio abaixo.
            </CardDescription>
          </CardHeader>
          <CardContent>
            <AdvertisementForm 
              advertisement={advertisement}
              vehicles={vehicles} 
              users={users}
              statuses={statuses}
              isEdit={true}
            />
          </CardContent>
        </Card>
      </div>
    </AdminLayout>
  );
}
