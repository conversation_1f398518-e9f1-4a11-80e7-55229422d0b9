<?php

namespace Database\Seeders;

// use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\Hash;

class DatabaseSeeder extends Seeder
{
    /**
     * Seed the application's database.
     */
    public function run(): void
    {
        $this->call([
            PermissionTableSeeder::class,
            UserSeeder::class,
            PermissionSeeder::class,
            CategorySeeder::class,
            BrandSeeder::class,
            VehicleSeeder::class,
            PartSeeder::class,
        ]);

        // Cria um usuário administrador padrão
        $admin = \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Administrator',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );
        
        // Atribui a função de admin
        $admin->assignRole('admin');

        // Cria um usuário moderador de exemplo
        $moderator = \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Moderator',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );
        
        // Atribui a função de moderador
        $moderator->assignRole('moderator');

        // Cria um usuário normal de exemplo
        $user = \App\Models\User::firstOrCreate(
            ['email' => '<EMAIL>'],
            [
                'name' => 'Regular User',
                'password' => bcrypt('password'),
                'email_verified_at' => now(),
            ]
        );
        
        // Atribui a função de usuário
        $user->assignRole('user');
    }
}
