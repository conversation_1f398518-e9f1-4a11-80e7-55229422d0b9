<?php

use Illuminate\Support\Facades\Route;
use Inertia\Inertia;

use App\Http\Controllers\Public\CategoryController;

Route::get('/', [\App\Http\Controllers\Public\WelcomeController::class, 'index'])->name('home');

// Rotas de categorias
Route::get('/categorias', [CategoryController::class, 'index'])->name('categories.index');
Route::get('/categorias/{category:slug}', [CategoryController::class, 'show'])->name('categories.show');

// Rotas públicas
Route::get('/veiculos', [\App\Http\Controllers\Public\VehicleController::class, 'index'])->name('vehicles.index');
Route::get('/veiculos/{vehicle:slug}', [\App\Http\Controllers\Public\VehicleController::class, 'show'])->name('vehicles.show');

// Rotas autenticadas
Route::middleware(['auth', 'verified'])->group(function () {
    // Dashboard
    Route::get('dashboard', function () {
        return Inertia::render('dashboard');
    })->name('dashboard');

    // Admin - Veículos
    Route::prefix('admin/veiculos')->name('admin.vehicles.')->middleware('can:admin')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\VehicleController::class, 'index'])->name('index');
        Route::get('/novo', [\App\Http\Controllers\Admin\VehicleController::class, 'create'])->name('create');
        Route::post('/', [\App\Http\Controllers\Admin\VehicleController::class, 'store'])->name('store');
        Route::get('/{vehicle}/editar', [\App\Http\Controllers\Admin\VehicleController::class, 'edit'])->name('edit');
        Route::put('/{vehicle}', [\App\Http\Controllers\Admin\VehicleController::class, 'update'])->name('update');
        Route::delete('/{vehicle}', [\App\Http\Controllers\Admin\VehicleController::class, 'destroy'])->name('destroy');
    });

        // Admin - Peças
    Route::prefix('admin/pecas')->name('admin.parts.')->middleware('can:view_any_part')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\PartController::class, 'index'])->name('index');
        Route::get('/nova', [\App\Http\Controllers\Admin\PartController::class, 'create'])->name('create');
        Route::post('/', [\App\Http\Controllers\Admin\PartController::class, 'store'])->name('store');
        Route::get('/{part}', [\App\Http\Controllers\Admin\PartController::class, 'show'])->name('show');
        Route::get('/{part}/editar', [\App\Http\Controllers\Admin\PartController::class, 'edit'])->name('edit');
        Route::put('/{part}', [\App\Http\Controllers\Admin\PartController::class, 'update'])->name('update');
        Route::delete('/{part}', [\App\Http\Controllers\Admin\PartController::class, 'destroy'])->name('destroy');
    });

    // Admin - Anúncios
    Route::prefix('admin/anuncios')->name('admin.advertisements.')->middleware('can:view_any_advertisement')->group(function () {
        Route::get('/', [\App\Http\Controllers\Admin\AdvertisementController::class, 'index'])->name('index');
        Route::get('/novo', [\App\Http\Controllers\Admin\AdvertisementController::class, 'create'])->name('create');
        Route::post('/', [\App\Http\Controllers\Admin\AdvertisementController::class, 'store'])->name('store');
        Route::get('/{advertisement}', [\App\Http\Controllers\Admin\AdvertisementController::class, 'show'])->name('show');
        Route::get('/{advertisement}/editar', [\App\Http\Controllers\Admin\AdvertisementController::class, 'edit'])->name('edit');
        Route::put('/{advertisement}', [\App\Http\Controllers\Admin\AdvertisementController::class, 'update'])->name('update');
        Route::delete('/{advertisement}', [\App\Http\Controllers\Admin\AdvertisementController::class, 'destroy'])->name('destroy');
        
        // Ações de moderação
        Route::post('/{advertisement}/aprovar', [\App\Http\Controllers\Admin\AdvertisementController::class, 'approve'])
            ->name('approve')
            ->middleware('can:approve_advertisement');
            
        Route::post('/{advertisement}/rejeitar', [\App\Http\Controllers\Admin\AdvertisementController::class, 'reject'])
            ->name('reject')
            ->middleware('can:reject_advertisement');
            
        Route::post('/{advertisement}/publicar', [\App\Http\Controllers\Admin\AdvertisementController::class, 'publish'])
            ->name('publish')
            ->middleware('can:publish_advertisement');
            
        Route::post('/{advertisement}/vender', [\App\Http\Controllers\Admin\AdvertisementController::class, 'markAsSold'])
            ->name('markAsSold')
            ->middleware('can:mark_sold_advertisement');
    });
});

require __DIR__.'/settings.php';
require __DIR__.'/auth.php';
